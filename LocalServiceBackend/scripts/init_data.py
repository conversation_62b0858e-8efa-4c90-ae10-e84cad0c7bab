"""
初始化数据脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import connect_to_mongo
from app.models.category import Category


def init_categories():
    """初始化服务分类数据"""
    categories_data = [
        {
            "name": "家庭维修",
            "description": "家电维修、水电维修、装修等服务",
            "icon": "build",
            "color": "#FF6B6B",
            "slug": "home-repair",
            "sort_order": 1
        },
        {
            "name": "清洁服务",
            "description": "家庭清洁、办公室清洁、深度清洁等",
            "icon": "cleaning-services",
            "color": "#4ECDC4",
            "slug": "cleaning",
            "sort_order": 2
        },
        {
            "name": "美容美发",
            "description": "理发、美容、美甲、化妆等服务",
            "icon": "content-cut",
            "color": "#45B7D1",
            "slug": "beauty",
            "sort_order": 3
        },
        {
            "name": "家教培训",
            "description": "学科辅导、技能培训、语言学习等",
            "icon": "school",
            "color": "#96CEB4",
            "slug": "tutoring",
            "sort_order": 4
        },
        {
            "name": "宠物服务",
            "description": "宠物美容、遛狗、宠物寄养等服务",
            "icon": "pets",
            "color": "#FFEAA7",
            "slug": "pet-care",
            "sort_order": 5
        },
        {
            "name": "搬家服务",
            "description": "搬家、货运、家具安装等服务",
            "icon": "local-shipping",
            "color": "#DDA0DD",
            "slug": "moving",
            "sort_order": 6
        },
        {
            "name": "摄影摄像",
            "description": "婚礼摄影、活动拍摄、证件照等",
            "icon": "camera-alt",
            "color": "#74B9FF",
            "slug": "photography",
            "sort_order": 7
        },
        {
            "name": "健康护理",
            "description": "按摩、理疗、护理、健康咨询等",
            "icon": "healing",
            "color": "#A29BFE",
            "slug": "healthcare",
            "sort_order": 8
        },
        {
            "name": "汽车服务",
            "description": "洗车、维修、代驾、陪练等服务",
            "icon": "directions-car",
            "color": "#FD79A8",
            "slug": "automotive",
            "sort_order": 9
        },
        {
            "name": "其他服务",
            "description": "其他各类生活服务",
            "icon": "more-horiz",
            "color": "#FDCB6E",
            "slug": "others",
            "sort_order": 10
        }
    ]
    
    print("🔄 初始化服务分类...")
    
    for cat_data in categories_data:
        # 检查是否已存在
        existing = Category.objects(slug=cat_data["slug"]).first()
        if existing:
            print(f"  ⏭️  分类 '{cat_data['name']}' 已存在，跳过")
            continue
        
        # 创建新分类
        category = Category(**cat_data)
        category.save()
        print(f"  ✅ 创建分类: {cat_data['name']}")
    
    print("✅ 服务分类初始化完成")


def main():
    """主函数"""
    print("🚀 开始初始化数据...")
    
    # 连接数据库
    connect_to_mongo()
    
    # 初始化分类
    init_categories()
    
    print("🎉 数据初始化完成！")


if __name__ == "__main__":
    main()
