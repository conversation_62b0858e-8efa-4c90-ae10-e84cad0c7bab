"""
API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_login():
    """测试登录"""
    print("🔍 测试用户登录...")
    
    # 测试管理员登录
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"登录成功: {result['data']['user']['full_name']}")
        token = result['data']['access_token']
        print(f"Token: {token[:50]}...")
        return token
    else:
        print(f"登录失败: {response.text}")
        return None
    print()

def test_get_categories(token=None):
    """测试获取分类"""
    print("🔍 测试获取服务分类...")
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    response = requests.get(f"{BASE_URL}/api/v1/services/categories/", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        categories = result['data']
        print(f"分类数量: {len(categories)}")
        for cat in categories[:3]:  # 显示前3个
            print(f"  - {cat['name']}: {cat['service_count']} 个服务")
    else:
        print(f"获取分类失败: {response.text}")
    print()

def test_get_services(token=None):
    """测试获取服务列表"""
    print("🔍 测试获取服务列表...")
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    params = {
        "page": 1,
        "limit": 5
    }
    
    response = requests.get(f"{BASE_URL}/api/v1/services/", headers=headers, params=params)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        services = result['data']
        pagination = result['pagination']
        print(f"服务数量: {len(services)}")
        print(f"总数: {pagination['total']}, 页数: {pagination['total_pages']}")
        
        for service in services[:2]:  # 显示前2个
            print(f"  - {service['title']}")
            print(f"    价格: ¥{service['price']}/{service['price_unit']}")
            print(f"    评分: {service['rating']} ({service['total_reviews']} 评价)")
    else:
        print(f"获取服务失败: {response.text}")
    print()

def test_user_profile(token):
    """测试获取用户资料"""
    if not token:
        print("❌ 需要登录token")
        return
        
    print("🔍 测试获取用户资料...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/v1/users/profile", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        user = result['data']
        print(f"用户: {user['full_name']} ({user['email']})")
        print(f"角色: {'服务提供者' if user['is_provider'] else '普通用户'}")
        print(f"验证状态: {'已验证' if user['is_verified'] else '未验证'}")
    else:
        print(f"获取用户资料失败: {response.text}")
    print()

def main():
    """主测试函数"""
    print("🚀 开始API测试...")
    print("=" * 50)
    
    # 测试基础功能
    test_health()
    
    # 测试登录
    token = test_login()
    
    # 测试分类
    test_get_categories(token)
    
    # 测试服务列表
    test_get_services(token)
    
    # 测试用户资料
    if token:
        test_user_profile(token)
    
    print("✅ API测试完成！")

if __name__ == "__main__":
    main()
