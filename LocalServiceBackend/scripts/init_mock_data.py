"""
模拟数据初始化脚本
"""
import sys
import os
import random
from datetime import datetime, timedelta
from faker import Faker

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import connect_to_mongo
from app.models.user import User, UserProfile
from app.models.category import Category
from app.models.service import Service
from app.models.order import Order
from app.models.review import Review
from app.models.message import Message, Conversation
from app.models.notification import Notification
from app.models.payment import Payment

# 初始化Faker，使用中文
fake = Faker('zh_CN')


def clear_all_data():
    """清空所有数据"""
    print("🗑️  清空现有数据...")
    
    collections = [
        User, UserProfile, Category, Service, Order, 
        Review, Message, Conversation, Notification, Payment
    ]
    
    for collection in collections:
        count = collection.objects.count()
        if count > 0:
            collection.drop_collection()
            print(f"  ✅ 清空 {collection.__name__}: {count} 条记录")


def init_categories():
    """初始化服务分类数据"""
    categories_data = [
        {
            "name": "家庭维修",
            "description": "家电维修、水电维修、装修等服务",
            "icon": "build",
            "color": "#FF6B6B",
            "slug": "home-repair",
            "sort_order": 1
        },
        {
            "name": "清洁服务",
            "description": "家庭清洁、办公室清洁、深度清洁等",
            "icon": "cleaning-services",
            "color": "#4ECDC4",
            "slug": "cleaning",
            "sort_order": 2
        },
        {
            "name": "美容美发",
            "description": "理发、美容、美甲、化妆等服务",
            "icon": "content-cut",
            "color": "#45B7D1",
            "slug": "beauty",
            "sort_order": 3
        },
        {
            "name": "家教培训",
            "description": "学科辅导、技能培训、语言学习等",
            "icon": "school",
            "color": "#96CEB4",
            "slug": "tutoring",
            "sort_order": 4
        },
        {
            "name": "宠物服务",
            "description": "宠物美容、遛狗、宠物寄养等服务",
            "icon": "pets",
            "color": "#FFEAA7",
            "slug": "pet-care",
            "sort_order": 5
        },
        {
            "name": "搬家服务",
            "description": "搬家、货运、家具安装等服务",
            "icon": "local-shipping",
            "color": "#DDA0DD",
            "slug": "moving",
            "sort_order": 6
        },
        {
            "name": "摄影摄像",
            "description": "婚礼摄影、活动拍摄、证件照等",
            "icon": "camera-alt",
            "color": "#74B9FF",
            "slug": "photography",
            "sort_order": 7
        },
        {
            "name": "健康护理",
            "description": "按摩、理疗、护理、健康咨询等",
            "icon": "healing",
            "color": "#A29BFE",
            "slug": "healthcare",
            "sort_order": 8
        },
        {
            "name": "汽车服务",
            "description": "洗车、维修、代驾、陪练等服务",
            "icon": "directions-car",
            "color": "#FD79A8",
            "slug": "automotive",
            "sort_order": 9
        },
        {
            "name": "其他服务",
            "description": "其他各类生活服务",
            "icon": "more-horiz",
            "color": "#FDCB6E",
            "slug": "others",
            "sort_order": 10
        }
    ]
    
    print("🔄 初始化服务分类...")
    categories = []
    
    for cat_data in categories_data:
        category = Category(**cat_data)
        category.save()
        categories.append(category)
        print(f"  ✅ 创建分类: {cat_data['name']}")
    
    print(f"✅ 服务分类初始化完成，共创建 {len(categories)} 个分类")
    return categories


def init_users():
    """初始化用户数据"""
    print("🔄 初始化用户数据...")
    
    users = []
    
    # 创建管理员用户
    admin_user = User(
        email="<EMAIL>",
        full_name="系统管理员",
        phone="13800000000",
        is_verified=True,
        is_active=True,
        is_provider=True
    )
    admin_user.set_password("admin123")
    admin_user.save()
    
    # 创建管理员资料
    admin_profile = UserProfile(
        user=admin_user,
        bio="系统管理员账户",
        location="北京市",
        rating=5.0,
        total_reviews=0,
        total_services=0,
        total_orders=0
    )
    admin_profile.save()
    users.append(admin_user)
    print(f"  ✅ 创建管理员用户: {admin_user.email}")
    
    # 创建普通用户和服务提供者
    for i in range(50):
        is_provider = random.choice([True, False])
        
        user = User(
            email=fake.email(),
            full_name=fake.name(),
            phone=fake.phone_number(),
            avatar_url=f"https://api.dicebear.com/7.x/avataaars/svg?seed={i}",
            is_verified=random.choice([True, False]),
            is_active=True,
            is_provider=is_provider
        )
        user.set_password("123456")
        user.save()
        
        # 创建用户资料
        profile = UserProfile(
            user=user,
            bio=fake.text(max_nb_chars=200) if random.choice([True, False]) else None,
            location=fake.city(),
            rating=round(random.uniform(3.0, 5.0), 1),
            total_reviews=random.randint(0, 50),
            total_services=random.randint(0, 10) if is_provider else 0,
            total_orders=random.randint(0, 30)
        )
        profile.save()
        users.append(user)
    
    print(f"✅ 用户数据初始化完成，共创建 {len(users)} 个用户")
    return users


def init_services(users, categories):
    """初始化服务数据"""
    print("🔄 初始化服务数据...")

    # 服务模板数据
    service_templates = {
        "home-repair": [
            {"title": "专业家电维修", "desc": "提供各种家电维修服务，经验丰富，价格合理", "price": 120},
            {"title": "水电维修服务", "desc": "专业水电工，解决各种水电问题", "price": 150},
            {"title": "家具安装服务", "desc": "专业家具安装，快速高效", "price": 80},
            {"title": "墙面粉刷", "desc": "专业墙面粉刷，环保材料", "price": 200},
        ],
        "cleaning": [
            {"title": "家庭深度清洁", "desc": "专业家庭清洁服务，让您的家焕然一新", "price": 180},
            {"title": "办公室清洁", "desc": "专业办公室清洁，营造舒适工作环境", "price": 250},
            {"title": "地毯清洗", "desc": "专业地毯清洗，去除污渍异味", "price": 100},
            {"title": "玻璃清洁", "desc": "专业玻璃清洁，让窗户明亮如新", "price": 60},
        ],
        "beauty": [
            {"title": "专业理发服务", "desc": "时尚造型设计，让您焕发新颜", "price": 80},
            {"title": "美甲服务", "desc": "专业美甲，多种款式选择", "price": 120},
            {"title": "化妆服务", "desc": "专业化妆师，打造完美妆容", "price": 200},
            {"title": "美容护理", "desc": "专业美容护理，让肌肤更加光滑", "price": 300},
        ],
        "tutoring": [
            {"title": "小学数学辅导", "desc": "专业小学数学辅导，提高学习成绩", "price": 100},
            {"title": "英语口语培训", "desc": "外教一对一英语口语培训", "price": 200},
            {"title": "钢琴教学", "desc": "专业钢琴老师，一对一教学", "price": 150},
            {"title": "书法培训", "desc": "传统书法培训，修身养性", "price": 80},
        ],
        "pet-care": [
            {"title": "宠物美容", "desc": "专业宠物美容，让您的爱宠更加可爱", "price": 120},
            {"title": "遛狗服务", "desc": "专业遛狗服务，让您的爱犬得到充分运动", "price": 50},
            {"title": "宠物寄养", "desc": "安全可靠的宠物寄养服务", "price": 80},
            {"title": "宠物训练", "desc": "专业宠物训练，培养良好习惯", "price": 200},
        ]
    }

    services = []
    providers = [user for user in users if user.is_provider]

    for provider in providers:
        # 每个服务提供者创建1-3个服务
        num_services = random.randint(1, 3)

        for _ in range(num_services):
            category = random.choice(categories)

            # 根据分类选择服务模板
            templates = service_templates.get(category.slug, [
                {"title": f"{category.name}服务", "desc": f"专业{category.name}，服务优质", "price": 100}
            ])
            template = random.choice(templates)

            # 添加一些随机性
            price_variation = random.uniform(0.8, 1.5)
            final_price = round(template["price"] * price_variation, 2)

            service = Service(
                title=template["title"] + f" - {provider.full_name}",
                description=template["desc"] + f"\n\n服务特色：{fake.text(max_nb_chars=100)}",
                user=provider,
                category=category,
                price=final_price,
                price_unit=random.choice(['hour', 'day', 'project']),
                location=fake.city(),
                latitude=round(random.uniform(39.8, 40.2), 6),
                longitude=round(random.uniform(116.2, 116.6), 6),
                images=[
                    f"https://picsum.photos/400/300?random={random.randint(1, 1000)}",
                    f"https://picsum.photos/400/300?random={random.randint(1, 1000)}"
                ],
                tags=random.sample(['专业', '快速', '优质', '实惠', '上门', '24小时'], k=random.randint(2, 4)),
                features=random.sample(['免费咨询', '质量保证', '售后服务', '价格透明'], k=random.randint(1, 3)),
                status='active',
                rating=round(random.uniform(3.5, 5.0), 1),
                total_reviews=random.randint(0, 50),
                total_orders=random.randint(0, 100),
                view_count=random.randint(10, 500)
            )
            service.save()
            services.append(service)

            # 更新分类的服务数量
            category.service_count += 1
            category.save()

    print(f"✅ 服务数据初始化完成，共创建 {len(services)} 个服务")
    return services


def init_orders(users, services):
    """初始化订单数据"""
    print("🔄 初始化订单数据...")

    orders = []
    customers = [user for user in users if not user.is_provider or random.choice([True, False])]

    for _ in range(200):
        service = random.choice(services)
        customer = random.choice(customers)

        # 确保客户不是服务提供者本人
        if customer == service.user:
            continue

        # 随机生成订单日期（过去30天到未来30天）
        days_offset = random.randint(-30, 30)
        scheduled_date = datetime.utcnow() + timedelta(days=days_offset)

        # 根据日期确定订单状态
        if days_offset < -7:
            status = random.choice(['completed', 'cancelled'])
        elif days_offset < 0:
            status = random.choice(['completed', 'in_progress'])
        elif days_offset < 7:
            status = random.choice(['confirmed', 'in_progress'])
        else:
            status = 'pending'

        order = Order(
            service=service,
            customer=customer,
            provider=service.user,
            status=status,
            scheduled_date=scheduled_date,
            scheduled_time=f"{random.randint(8, 18)}:00-{random.randint(8, 18)+2}:00",
            total_amount=service.price * random.uniform(0.8, 1.2),
            payment_status='paid' if status in ['completed', 'in_progress'] else random.choice(['pending', 'paid']),
            payment_method=random.choice(['alipay', 'wechat', 'bank_card']),
            notes=fake.text(max_nb_chars=100) if random.choice([True, False]) else None,
            provider_notes=fake.text(max_nb_chars=50) if status != 'pending' else None,
            completed_at=scheduled_date + timedelta(hours=2) if status == 'completed' else None,
            cancelled_at=scheduled_date if status == 'cancelled' else None,
            cancellation_reason="客户临时有事" if status == 'cancelled' else None
        )
        order.save()
        orders.append(order)

    print(f"✅ 订单数据初始化完成，共创建 {len(orders)} 个订单")
    return orders


def init_reviews(orders):
    """初始化评价数据"""
    print("🔄 初始化评价数据...")

    reviews = []
    completed_orders = [order for order in orders if order.status == 'completed']

    # 为70%的已完成订单创建评价
    review_orders = random.sample(completed_orders, int(len(completed_orders) * 0.7))

    review_comments = [
        "服务很好，师傅很专业，下次还会找他",
        "价格合理，服务到位，推荐！",
        "非常满意，工作认真负责",
        "技术过硬，人也很nice",
        "服务态度很好，质量也不错",
        "准时到达，工作效率高",
        "专业水平很高，值得信赖",
        "服务周到，价格实惠",
        "工作细致，让人放心",
        "下次还会选择这家服务"
    ]

    for order in review_orders:
        rating = random.choices([3, 4, 5], weights=[10, 30, 60])[0]  # 偏向高评分

        review = Review(
            order=order,
            service=order.service,
            reviewer=order.customer,
            reviewee=order.provider,
            rating=rating,
            comment=random.choice(review_comments),
            images=[f"https://picsum.photos/300/200?random={random.randint(1, 1000)}"] if random.choice([True, False]) else [],
            tags=random.sample(['专业', '准时', '服务好', '价格合理', '态度好'], k=random.randint(1, 3)),
            is_visible=True,
            is_anonymous=random.choice([True, False]),
            reply=f"谢谢您的好评，我们会继续努力！" if random.choice([True, False]) else None,
            reply_at=datetime.utcnow() if random.choice([True, False]) else None
        )
        review.save()
        reviews.append(review)

    print(f"✅ 评价数据初始化完成，共创建 {len(reviews)} 个评价")
    return reviews


def init_messages_and_conversations(users, orders):
    """初始化消息和会话数据"""
    print("🔄 初始化消息和会话数据...")

    conversations = []
    messages = []

    # 为部分订单创建会话
    sample_orders = random.sample(orders, min(50, len(orders)))

    for order in sample_orders:
        # 创建会话
        conversation = Conversation(
            participants=[order.customer, order.provider],
            order=order,
            is_active=True,
            unread_counts={
                str(order.customer.id): random.randint(0, 3),
                str(order.provider.id): random.randint(0, 2)
            }
        )
        conversation.save()
        conversations.append(conversation)

        # 为会话创建消息
        num_messages = random.randint(2, 8)
        conversation_messages = []

        for i in range(num_messages):
            sender = random.choice([order.customer, order.provider])
            receiver = order.provider if sender == order.customer else order.customer

            message_contents = [
                "您好，请问什么时候可以上门服务？",
                "我明天下午有时间，可以安排吗？",
                "好的，没问题，我会准时到达",
                "需要准备什么工具吗？",
                "不用，我会带齐所有工具",
                "服务完成了，请您验收一下",
                "非常满意，谢谢您的专业服务",
                "不客气，有问题随时联系我"
            ]

            message = Message(
                sender=sender,
                receiver=receiver,
                order=order,
                content=random.choice(message_contents),
                message_type='text',
                is_read=random.choice([True, False]),
                created_at=datetime.now() - timedelta(hours=random.randint(1, 72)),
                read_at=datetime.now() - timedelta(hours=random.randint(0, 24)) if random.choice([True, False]) else None
            )
            message.save()
            messages.append(message)
            conversation_messages.append(message)

        # 更新会话的最后消息
        if conversation_messages:
            last_message = max(conversation_messages, key=lambda m: m.created_at)
            conversation.last_message = last_message
            conversation.last_message_at = last_message.created_at
            conversation.save()

    print(f"✅ 消息数据初始化完成，共创建 {len(conversations)} 个会话，{len(messages)} 条消息")
    return conversations, messages


def init_notifications(users, orders):
    """初始化通知数据"""
    print("🔄 初始化通知数据...")

    notifications = []

    notification_templates = [
        {"type": "order", "title": "新订单通知", "message": "您有一个新的服务订单"},
        {"type": "order", "title": "订单确认", "message": "您的订单已被确认"},
        {"type": "order", "title": "订单完成", "message": "您的订单已完成，请及时评价"},
        {"type": "payment", "title": "支付成功", "message": "您的订单支付已成功"},
        {"type": "review", "title": "新评价", "message": "您收到了一条新评价"},
        {"type": "system", "title": "系统通知", "message": "欢迎使用本地生活服务平台"},
        {"type": "message", "title": "新消息", "message": "您有一条新消息"}
    ]

    for user in users:
        # 为每个用户创建5-15个通知
        num_notifications = random.randint(5, 15)

        for _ in range(num_notifications):
            template = random.choice(notification_templates)

            notification = Notification(
                user=user,
                title=template["title"],
                message=template["message"],
                type=template["type"],
                data={
                    "order_id": str(random.choice(orders).id) if template["type"] == "order" else None,
                    "extra_info": fake.text(max_nb_chars=50)
                },
                is_read=random.choice([True, False]),
                is_sent=True,
                created_at=datetime.now() - timedelta(hours=random.randint(1, 168)),  # 过去一周
                read_at=datetime.now() - timedelta(hours=random.randint(0, 24)) if random.choice([True, False]) else None,
                sent_at=datetime.now() - timedelta(hours=random.randint(1, 168))
            )
            notification.save()
            notifications.append(notification)

    print(f"✅ 通知数据初始化完成，共创建 {len(notifications)} 个通知")
    return notifications


def init_payments(orders):
    """初始化支付数据"""
    print("🔄 初始化支付数据...")

    payments = []
    paid_orders = [order for order in orders if order.payment_status == 'paid']

    for order in paid_orders:
        payment = Payment(
            order=order,
            user=order.customer,
            amount=order.total_amount,
            currency='CNY',
            payment_method=order.payment_method,
            status='completed',
            transaction_id=f"TXN{random.randint(100000000, 999999999)}",
            payment_gateway=random.choice(['alipay', 'wechat', 'stripe']),
            gateway_response={
                "transaction_id": f"GW{random.randint(100000000, 999999999)}",
                "status": "success",
                "message": "Payment completed successfully"
            },
            notes=f"订单 {order.id} 的支付",
            created_at=order.scheduled_date - timedelta(hours=random.randint(1, 24)),
            completed_at=order.scheduled_date - timedelta(hours=random.randint(0, 12))
        )
        payment.save()
        payments.append(payment)

    print(f"✅ 支付数据初始化完成，共创建 {len(payments)} 个支付记录")
    return payments


def main():
    """主函数"""
    print("🚀 开始初始化模拟数据...")
    print("⚠️  警告：此操作将清空所有现有数据！")

    # 连接数据库
    connect_to_mongo()

    # 清空现有数据
    clear_all_data()

    # 初始化数据
    categories = init_categories()
    users = init_users()
    services = init_services(users, categories)
    orders = init_orders(users, services)
    reviews = init_reviews(orders)
    conversations, messages = init_messages_and_conversations(users, orders)
    notifications = init_notifications(users, orders)
    payments = init_payments(orders)

    # 统计信息
    print("\n📊 数据初始化完成统计:")
    print(f"  👥 用户: {len(users)} 个")
    print(f"  📂 分类: {len(categories)} 个")
    print(f"  🛍️  服务: {len(services)} 个")
    print(f"  📋 订单: {len(orders)} 个")
    print(f"  ⭐ 评价: {len(reviews)} 个")
    print(f"  💬 会话: {len(conversations)} 个")
    print(f"  📨 消息: {len(messages)} 条")
    print(f"  🔔 通知: {len(notifications)} 个")
    print(f"  💳 支付: {len(payments)} 个")

    print("\n🎉 模拟数据初始化完成！")
    print("\n📝 测试账户信息:")
    print("  管理员账户: <EMAIL> / admin123")
    print("  普通用户密码: 123456")
    print("\n🌐 API文档: http://localhost:8000/docs")


if __name__ == "__main__":
    main()
