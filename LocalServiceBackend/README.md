# LocalService Backend API 🚀

本地生活服务平台后端API，基于FastAPI + MongoDB + JWT构建。

## 📋 功能特性

### 🔐 用户系统
- 用户注册/登录
- JWT认证
- 用户资料管理
- 密码修改和重置

### 🛍️ 服务管理
- 服务发布和编辑
- 服务分类管理
- 服务搜索和筛选
- 服务详情查看

### 📋 订单系统
- 订单创建和管理
- 订单状态跟踪
- 订单历史查看

### ⭐ 评价系统
- 服务评价和评分
- 评价展示和管理

### 💬 消息系统
- 用户间消息
- 系统通知

### 💳 支付系统
- 支付记录管理
- 多种支付方式支持

## 🛠️ 技术栈

- **FastAPI** - 现代化Python Web框架
- **MongoDB** - NoSQL数据库
- **MongoEngine** - MongoDB ORM
- **JWT** - JSON Web Token认证
- **Pydantic** - 数据验证和序列化
- **Uvicorn** - ASGI服务器

## 🚀 快速开始

### 环境要求
- Python 3.8+
- MongoDB 4.4+

### 安装依赖
```bash
cd LocalServiceBackend
pip install -r requirements.txt
```

### 环境配置
1. 复制环境变量文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置数据库和其他设置：
```env
MONGODB_URL=mongodb://localhost:27017/localservice
MONGODB_DB_NAME=localservice
SECRET_KEY=your-secret-key-here
DEBUG=True
```

### 初始化数据
```bash
python scripts/init_data.py
```

### 启动服务
```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 访问API文档
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📁 项目结构

```
LocalServiceBackend/
├── app/                          # 应用核心代码
│   ├── api/                      # API路由
│   │   ├── auth.py              # 认证相关API
│   │   ├── users.py             # 用户相关API
│   │   └── services.py          # 服务相关API
│   ├── models/                   # 数据模型
│   │   ├── user.py              # 用户模型
│   │   ├── service.py           # 服务模型
│   │   ├── order.py             # 订单模型
│   │   ├── review.py            # 评价模型
│   │   ├── message.py           # 消息模型
│   │   ├── notification.py      # 通知模型
│   │   └── payment.py           # 支付模型
│   ├── schemas/                  # Pydantic模式
│   │   ├── auth.py              # 认证相关模式
│   │   ├── user.py              # 用户相关模式
│   │   ├── service.py           # 服务相关模式
│   │   └── common.py            # 通用模式
│   ├── auth.py                   # 认证工具
│   ├── config.py                 # 配置管理
│   └── database.py               # 数据库连接
├── scripts/                      # 脚本文件
│   └── init_data.py             # 初始化数据脚本
├── main.py                       # 主应用文件
├── requirements.txt              # 依赖包列表
├── .env.example                  # 环境变量示例
└── README.md                     # 项目说明
```

## 🔌 API接口

### 认证接口
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/change-password` - 修改密码

### 用户接口
- `GET /api/v1/users/profile` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料
- `GET /api/v1/users/stats` - 获取用户统计信息

### 服务接口
- `GET /api/v1/services/` - 获取服务列表
- `GET /api/v1/services/{id}` - 获取服务详情
- `POST /api/v1/services/` - 创建服务
- `PUT /api/v1/services/{id}` - 更新服务
- `DELETE /api/v1/services/{id}` - 删除服务
- `GET /api/v1/services/categories/` - 获取服务分类

## 🔒 认证方式

API使用JWT Bearer Token认证：

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/users/profile
```

## 📊 数据模型

### 用户 (User)
- 基本信息：邮箱、姓名、手机、头像
- 认证信息：密码、验证状态
- 角色信息：是否为服务提供者

### 服务 (Service)
- 基本信息：标题、描述、价格、位置
- 分类信息：所属分类
- 媒体信息：图片、标签、特色
- 统计信息：评分、评价数、订单数

### 订单 (Order)
- 关联信息：服务、客户、服务商
- 预约信息：日期、时间
- 状态信息：订单状态、支付状态
- 备注信息：客户备注、服务商备注

## 🧪 测试

```bash
# 运行测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app
```

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t localservice-api .

# 运行容器
docker run -p 8000:8000 localservice-api
```

### 生产环境配置
1. 设置 `DEBUG=False`
2. 使用强密码作为 `SECRET_KEY`
3. 配置生产数据库
4. 设置适当的CORS策略

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License
