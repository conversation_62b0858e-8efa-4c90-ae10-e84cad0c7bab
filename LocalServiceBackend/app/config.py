"""
应用配置模块
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    """应用设置"""
    
    # 应用基础配置
    app_name: str = "LocalService API"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 数据库配置
    mongodb_url: str = "mongodb://localhost:27017/localservice"
    mongodb_db_name: str = "localservice"
    
    # JWT配置
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS配置
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:19006"]
    
    # 文件上传配置
    upload_dir: str = "uploads"
    max_file_size: int = 10485760  # 10MB
    
    # 邮件配置
    smtp_host: str = ""
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    
    @validator('allowed_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 创建全局设置实例
settings = Settings()
