"""
数据库连接和配置
"""
import mongoengine
from app.config import settings


def connect_to_mongo():
    """连接到MongoDB数据库"""
    try:
        mongoengine.connect(
            db=settings.mongodb_db_name,
            host=settings.mongodb_url,
            alias='default'
        )
        print(f"✅ 成功连接到MongoDB: {settings.mongodb_db_name}")
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise


def disconnect_from_mongo():
    """断开MongoDB连接"""
    try:
        mongoengine.disconnect()
        print("✅ 已断开MongoDB连接")
    except Exception as e:
        print(f"❌ 断开MongoDB连接失败: {e}")
