"""
消息和会话数据模型
"""
from datetime import datetime
from mongoengine import (
    Document, StringField, ListField, DateTimeField,
    ReferenceField, BooleanField, IntField, DictField
)
from .user import User
from .order import Order


class Message(Document):
    """消息模型"""
    
    # 发送者和接收者
    sender = ReferenceField(User, required=True)
    receiver = ReferenceField(User, required=True)
    
    # 关联订单（可选）
    order = ReferenceField(Order)
    
    # 消息内容
    content = StringField(required=True, max_length=2000)
    message_type = StringField(
        choices=['text', 'image', 'file', 'system'],
        default='text'
    )
    
    # 文件信息（如果是文件消息）
    file_url = StringField(max_length=500)
    file_name = StringField(max_length=255)
    file_size = IntField()
    
    # 状态
    is_read = BooleanField(default=False)
    is_deleted_by_sender = BooleanField(default=False)
    is_deleted_by_receiver = BooleanField(default=False)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    read_at = DateTimeField()
    
    meta = {
        'collection': 'messages',
        'indexes': [
            'sender',
            'receiver',
            'order',
            'is_read',
            'created_at',
            ('sender', 'receiver'),
            ('receiver', 'is_read'),
            ('order', 'created_at')
        ]
    }
    
    def to_dict(self, include_sender=False, include_receiver=False):
        """转换为字典"""
        data = {
            'id': str(self.id),
            'sender_id': str(self.sender.id),
            'receiver_id': str(self.receiver.id),
            'order_id': str(self.order.id) if self.order else None,
            'content': self.content,
            'message_type': self.message_type,
            'file_url': self.file_url,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None
        }
        
        if include_sender and self.sender:
            data['sender'] = self.sender.to_dict()
            
        if include_receiver and self.receiver:
            data['receiver'] = self.receiver.to_dict()
            
        return data


class Conversation(Document):
    """会话模型"""
    
    # 参与者
    participants = ListField(ReferenceField(User), required=True)
    
    # 关联订单（可选）
    order = ReferenceField(Order)
    
    # 最后一条消息
    last_message = ReferenceField(Message)
    last_message_at = DateTimeField()
    
    # 未读消息计数（每个参与者）
    unread_counts = DictField()  # {user_id: count}
    
    # 状态
    is_active = BooleanField(default=True)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'conversations',
        'indexes': [
            'participants',
            'order',
            'last_message_at',
            'is_active',
            'updated_at'
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self, include_participants=False, include_last_message=False):
        """转换为字典"""
        data = {
            'id': str(self.id),
            'participant_ids': [str(p.id) for p in self.participants],
            'order_id': str(self.order.id) if self.order else None,
            'last_message_id': str(self.last_message.id) if self.last_message else None,
            'last_message_at': self.last_message_at.isoformat() if self.last_message_at else None,
            'unread_counts': self.unread_counts,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_participants:
            data['participants'] = [p.to_dict() for p in self.participants]
            
        if include_last_message and self.last_message:
            data['last_message'] = self.last_message.to_dict()
            
        return data
