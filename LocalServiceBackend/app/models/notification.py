"""
通知数据模型
"""
from datetime import datetime
from mongoengine import (
    Document, StringField, DateTimeField, 
    ReferenceField, BooleanField, DictField
)
from .user import User


class Notification(Document):
    """通知模型"""
    
    # 接收者
    user = ReferenceField(User, required=True)
    
    # 通知内容
    title = StringField(required=True, max_length=200)
    message = StringField(required=True, max_length=1000)
    
    # 通知类型
    type = StringField(
        required=True,
        choices=['order', 'message', 'review', 'system', 'payment']
    )
    
    # 相关数据
    data = DictField()  # 存储相关的ID和其他数据
    
    # 状态
    is_read = BooleanField(default=False)
    is_sent = BooleanField(default=False)  # 是否已发送（推送通知）
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    read_at = DateTimeField()
    sent_at = DateTimeField()
    
    meta = {
        'collection': 'notifications',
        'indexes': [
            'user',
            'type',
            'is_read',
            'is_sent',
            'created_at',
            ('user', 'is_read'),
            ('user', 'type'),
            ('user', 'created_at')
        ]
    }
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'user_id': str(self.user.id),
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'data': self.data,
            'is_read': self.is_read,
            'is_sent': self.is_sent,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None
        }
