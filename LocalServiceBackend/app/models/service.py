"""
服务数据模型
"""
from datetime import datetime
from typing import List
from mongoengine import (
    Document, StringField, FloatField, IntField, ListField, 
    DateTimeField, ReferenceField, BooleanField
)
from .user import User
from .category import Category


class Service(Document):
    """服务模型"""
    
    # 基本信息
    title = StringField(required=True, max_length=200)
    description = StringField(required=True, max_length=2000)
    
    # 关联信息
    user = ReferenceField(User, required=True)  # 服务提供者
    category = ReferenceField(Category, required=True)
    
    # 价格信息
    price = FloatField(required=True, min_value=0)
    price_unit = StringField(
        required=True, 
        choices=['hour', 'day', 'project', 'other'],
        default='hour'
    )
    
    # 位置信息
    location = StringField(required=True, max_length=200)
    latitude = FloatField()
    longitude = FloatField()
    
    # 媒体文件
    images = ListField(StringField(max_length=500))  # 图片URL列表
    
    # 服务特性
    tags = ListField(StringField(max_length=50))  # 标签
    features = ListField(StringField(max_length=100))  # 服务特色
    
    # 状态和统计
    status = StringField(
        choices=['active', 'inactive', 'suspended'],
        default='active'
    )
    rating = FloatField(default=0.0, min_value=0.0, max_value=5.0)
    total_reviews = IntField(default=0, min_value=0)
    total_orders = IntField(default=0, min_value=0)
    view_count = IntField(default=0, min_value=0)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'services',
        'indexes': [
            'user',
            'category',
            'status',
            'rating',
            'price',
            'location',
            'created_at',
            ('status', 'rating'),
            ('category', 'status'),
            ('location', 'status')
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self, include_user=False, include_category=False):
        """转换为字典"""
        data = {
            'id': str(self.id),
            'title': self.title,
            'description': self.description,
            'user_id': str(self.user.id),
            'category_id': str(self.category.id),
            'price': self.price,
            'price_unit': self.price_unit,
            'location': self.location,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'images': self.images,
            'tags': self.tags,
            'features': self.features,
            'status': self.status,
            'rating': self.rating,
            'total_reviews': self.total_reviews,
            'total_orders': self.total_orders,
            'view_count': self.view_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_user and self.user:
            data['user'] = self.user.to_dict()
            
        if include_category and self.category:
            data['category'] = self.category.to_dict()
            
        return data
