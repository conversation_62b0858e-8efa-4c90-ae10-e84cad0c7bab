"""
用户相关数据模型
"""
from datetime import datetime
from typing import Optional
from mongoengine import Document, StringField, EmailField, BooleanField, DateTimeField, FloatField, IntField, ReferenceField
from passlib.context import CryptContext

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User(Document):
    """用户模型"""
    
    # 基本信息
    email = EmailField(required=True, unique=True, max_length=255)
    phone = StringField(max_length=20)
    full_name = StringField(required=True, max_length=100)
    avatar_url = StringField(max_length=500)
    
    # 认证信息
    hashed_password = StringField(required=True)
    is_verified = BooleanField(default=False)
    is_active = BooleanField(default=True)
    is_provider = BooleanField(default=False)  # 是否为服务提供者
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    last_login = DateTimeField()
    
    # 索引
    meta = {
        'collection': 'users',
        'indexes': [
            'email',
            'phone',
            'created_at',
            ('email', 'is_active')
        ]
    }
    
    def set_password(self, password: str):
        """设置密码"""
        self.hashed_password = pwd_context.hash(password)
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.hashed_password)
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'email': self.email,
            'phone': self.phone,
            'full_name': self.full_name,
            'avatar_url': self.avatar_url,
            'is_verified': self.is_verified,
            'is_active': self.is_active,
            'is_provider': self.is_provider,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }


class UserProfile(Document):
    """用户资料扩展模型"""
    
    user = ReferenceField(User, required=True, unique=True)
    bio = StringField(max_length=500)
    location = StringField(max_length=200)
    
    # 统计信息
    rating = FloatField(default=0.0, min_value=0.0, max_value=5.0)
    total_reviews = IntField(default=0, min_value=0)
    total_services = IntField(default=0, min_value=0)
    total_orders = IntField(default=0, min_value=0)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'user_profiles',
        'indexes': [
            'user',
            'rating',
            'location'
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'user_id': str(self.user.id),
            'bio': self.bio,
            'location': self.location,
            'rating': self.rating,
            'total_reviews': self.total_reviews,
            'total_services': self.total_services,
            'total_orders': self.total_orders,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
