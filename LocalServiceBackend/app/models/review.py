"""
评价数据模型
"""
from datetime import datetime
from mongoengine import (
    Document, StringField, IntField, ListField,
    DateTimeField, ReferenceField, BooleanField
)
from .user import User
from .service import Service
from .order import Order


class Review(Document):
    """评价模型"""
    
    # 关联信息
    order = ReferenceField(Order, required=True, unique=True)  # 每个订单只能评价一次
    service = ReferenceField(Service, required=True)
    reviewer = ReferenceField(User, required=True)  # 评价者
    reviewee = ReferenceField(User, required=True)  # 被评价者
    
    # 评价内容
    rating = IntField(required=True, min_value=1, max_value=5)
    comment = StringField(max_length=1000)
    images = ListField(StringField(max_length=500))  # 评价图片
    
    # 评价标签
    tags = ListField(StringField(max_length=50))  # 例如: ["专业", "准时", "服务好"]
    
    # 状态
    is_visible = BooleanField(default=True)  # 是否显示
    is_anonymous = BooleanField(default=False)  # 是否匿名评价
    
    # 回复
    reply = StringField(max_length=500)  # 服务商回复
    reply_at = DateTimeField()
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'reviews',
        'indexes': [
            'order',
            'service',
            'reviewer',
            'reviewee',
            'rating',
            'is_visible',
            'created_at',
            ('service', 'is_visible'),
            ('reviewee', 'is_visible'),
            ('rating', 'is_visible')
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self, include_reviewer=False, include_service=False):
        """转换为字典"""
        data = {
            'id': str(self.id),
            'order_id': str(self.order.id),
            'service_id': str(self.service.id),
            'reviewer_id': str(self.reviewer.id),
            'reviewee_id': str(self.reviewee.id),
            'rating': self.rating,
            'comment': self.comment,
            'images': self.images,
            'tags': self.tags,
            'is_visible': self.is_visible,
            'is_anonymous': self.is_anonymous,
            'reply': self.reply,
            'reply_at': self.reply_at.isoformat() if self.reply_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_reviewer and self.reviewer and not self.is_anonymous:
            data['reviewer'] = self.reviewer.to_dict()
            
        if include_service and self.service:
            data['service'] = self.service.to_dict()
            
        return data
