"""
订单数据模型
"""
from datetime import datetime
from mongoengine import (
    Document, StringField, FloatField, DateTimeField, 
    ReferenceField, BooleanField
)
from .user import User
from .service import Service


class Order(Document):
    """订单模型"""
    
    # 关联信息
    service = ReferenceField(Service, required=True)
    customer = ReferenceField(User, required=True)  # 客户
    provider = ReferenceField(User, required=True)  # 服务提供者
    
    # 订单状态
    status = StringField(
        required=True,
        choices=['pending', 'confirmed', 'in_progress', 'completed', 'cancelled'],
        default='pending'
    )
    
    # 预约信息
    scheduled_date = DateTimeField(required=True)
    scheduled_time = StringField(max_length=20)  # 例如: "14:00-16:00"
    
    # 价格信息
    total_amount = FloatField(required=True, min_value=0)
    
    # 支付信息
    payment_status = StringField(
        choices=['pending', 'paid', 'refunded'],
        default='pending'
    )
    payment_method = StringField(max_length=50)
    
    # 订单详情
    notes = StringField(max_length=1000)  # 客户备注
    provider_notes = StringField(max_length=1000)  # 服务商备注
    
    # 完成信息
    completed_at = DateTimeField()
    cancelled_at = DateTimeField()
    cancellation_reason = StringField(max_length=500)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'orders',
        'indexes': [
            'service',
            'customer',
            'provider',
            'status',
            'payment_status',
            'scheduled_date',
            'created_at',
            ('customer', 'status'),
            ('provider', 'status'),
            ('service', 'status')
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self, include_service=False, include_customer=False, include_provider=False):
        """转换为字典"""
        data = {
            'id': str(self.id),
            'service_id': str(self.service.id),
            'customer_id': str(self.customer.id),
            'provider_id': str(self.provider.id),
            'status': self.status,
            'scheduled_date': self.scheduled_date.isoformat() if self.scheduled_date else None,
            'scheduled_time': self.scheduled_time,
            'total_amount': self.total_amount,
            'payment_status': self.payment_status,
            'payment_method': self.payment_method,
            'notes': self.notes,
            'provider_notes': self.provider_notes,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'cancelled_at': self.cancelled_at.isoformat() if self.cancelled_at else None,
            'cancellation_reason': self.cancellation_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_service and self.service:
            data['service'] = self.service.to_dict()
            
        if include_customer and self.customer:
            data['customer'] = self.customer.to_dict()
            
        if include_provider and self.provider:
            data['provider'] = self.provider.to_dict()
            
        return data
