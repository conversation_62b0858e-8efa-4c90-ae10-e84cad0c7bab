"""
服务分类数据模型
"""
from datetime import datetime
from mongoengine import Document, StringField, DateTimeField, IntField, BooleanField


class Category(Document):
    """服务分类模型"""
    
    name = StringField(required=True, max_length=100, unique=True)
    description = StringField(max_length=500)
    icon = StringField(required=True, max_length=100)  # 图标名称
    color = StringField(required=True, max_length=20)  # 颜色代码
    slug = StringField(required=True, max_length=100, unique=True)  # URL友好的标识符
    
    # 统计信息
    service_count = IntField(default=0, min_value=0)
    
    # 排序和状态
    sort_order = IntField(default=0)
    is_active = BooleanField(default=True)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'categories',
        'indexes': [
            'name',
            'slug',
            'sort_order',
            'is_active'
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'icon': self.icon,
            'color': self.color,
            'slug': self.slug,
            'service_count': self.service_count,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
