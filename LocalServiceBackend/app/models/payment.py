"""
支付数据模型
"""
from datetime import datetime
from mongoengine import (
    Document, StringField, FloatField, DateTimeField, 
    ReferenceField, DictField
)
from .user import User
from .order import Order


class Payment(Document):
    """支付模型"""
    
    # 关联信息
    order = ReferenceField(Order, required=True)
    user = ReferenceField(User, required=True)  # 付款用户
    
    # 支付信息
    amount = FloatField(required=True, min_value=0)
    currency = StringField(default='CNY', max_length=10)
    
    # 支付方式
    payment_method = StringField(
        required=True,
        choices=['alipay', 'wechat', 'bank_card', 'balance', 'other']
    )
    
    # 支付状态
    status = StringField(
        required=True,
        choices=['pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled'],
        default='pending'
    )
    
    # 第三方支付信息
    transaction_id = StringField(max_length=100)  # 第三方交易ID
    payment_gateway = StringField(max_length=50)  # 支付网关
    gateway_response = DictField()  # 网关响应数据
    
    # 退款信息
    refund_amount = FloatField(default=0, min_value=0)
    refund_reason = StringField(max_length=500)
    refunded_at = DateTimeField()
    
    # 备注
    notes = StringField(max_length=500)
    
    # 时间戳
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    completed_at = DateTimeField()
    failed_at = DateTimeField()
    
    meta = {
        'collection': 'payments',
        'indexes': [
            'order',
            'user',
            'status',
            'payment_method',
            'transaction_id',
            'created_at',
            ('user', 'status'),
            ('order', 'status')
        ]
    }
    
    def save(self, *args, **kwargs):
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def to_dict(self, include_order=False, include_user=False):
        """转换为字典"""
        data = {
            'id': str(self.id),
            'order_id': str(self.order.id),
            'user_id': str(self.user.id),
            'amount': self.amount,
            'currency': self.currency,
            'payment_method': self.payment_method,
            'status': self.status,
            'transaction_id': self.transaction_id,
            'payment_gateway': self.payment_gateway,
            'refund_amount': self.refund_amount,
            'refund_reason': self.refund_reason,
            'refunded_at': self.refunded_at.isoformat() if self.refunded_at else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'failed_at': self.failed_at.isoformat() if self.failed_at else None
        }
        
        if include_order and self.order:
            data['order'] = self.order.to_dict()
            
        if include_user and self.user:
            data['user'] = self.user.to_dict()
            
        return data
