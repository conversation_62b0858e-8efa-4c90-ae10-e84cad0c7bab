"""
用户相关API路由
"""
from typing import List
from fastapi import APIRouter, HTTPException, status, Depends, Query
from app.schemas.user import UserUpdate, UserResponse, UserProfileUpdate, UserProfileResponse, UserStats
from app.schemas.common import ApiResponse, PaginationParams, PaginatedResponse
from app.models.user import User, UserProfile
from app.models.service import Service
from app.models.order import Order
from app.models.review import Review
from app.auth import get_current_active_user

router = APIRouter(prefix="/users", tags=["用户"])


@router.get("/profile", response_model=ApiResponse[UserResponse], summary="获取用户资料")
async def get_user_profile(current_user: User = Depends(get_current_active_user)):
    """获取当前用户的详细资料"""
    return ApiResponse(
        data=UserResponse(**current_user.to_dict()),
        message="User profile retrieved successfully"
    )


@router.put("/profile", response_model=ApiResponse[UserResponse], summary="更新用户资料")
async def update_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """
    更新用户基本资料
    
    - **full_name**: 姓名
    - **phone**: 手机号
    - **avatar_url**: 头像URL
    """
    try:
        # 更新用户信息
        if user_data.full_name is not None:
            current_user.full_name = user_data.full_name
        if user_data.phone is not None:
            # 检查手机号是否已被其他用户使用
            existing_user = User.objects(phone=user_data.phone, id__ne=current_user.id).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Phone number already in use"
                )
            current_user.phone = user_data.phone
        if user_data.avatar_url is not None:
            current_user.avatar_url = user_data.avatar_url
        
        current_user.save()
        
        return ApiResponse(
            data=UserResponse(**current_user.to_dict()),
            message="User profile updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )


@router.get("/profile/extended", response_model=ApiResponse[UserProfileResponse], summary="获取扩展用户资料")
async def get_extended_profile(current_user: User = Depends(get_current_active_user)):
    """获取用户的扩展资料信息"""
    profile = UserProfile.objects(user=current_user).first()
    if not profile:
        # 如果没有扩展资料，创建一个
        profile = UserProfile(user=current_user)
        profile.save()
    
    return ApiResponse(
        data=UserProfileResponse(**profile.to_dict()),
        message="Extended profile retrieved successfully"
    )


@router.put("/profile/extended", response_model=ApiResponse[UserProfileResponse], summary="更新扩展用户资料")
async def update_extended_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """
    更新用户扩展资料
    
    - **bio**: 个人简介
    - **location**: 所在地区
    """
    try:
        profile = UserProfile.objects(user=current_user).first()
        if not profile:
            profile = UserProfile(user=current_user)
        
        if profile_data.bio is not None:
            profile.bio = profile_data.bio
        if profile_data.location is not None:
            profile.location = profile_data.location
        
        profile.save()
        
        return ApiResponse(
            data=UserProfileResponse(**profile.to_dict()),
            message="Extended profile updated successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update extended profile: {str(e)}"
        )


@router.get("/stats", response_model=ApiResponse[UserStats], summary="获取用户统计信息")
async def get_user_stats(current_user: User = Depends(get_current_active_user)):
    """获取用户的统计信息"""
    try:
        # 统计服务数量
        total_services = Service.objects(user=current_user).count()
        
        # 统计订单数量
        total_orders = Order.objects(provider=current_user).count()
        
        # 统计评价数量和平均评分
        reviews = Review.objects(reviewee=current_user)
        total_reviews = reviews.count()
        average_rating = 0.0
        if total_reviews > 0:
            total_rating = sum([review.rating for review in reviews])
            average_rating = total_rating / total_reviews
        
        # 统计总收入（已完成的订单）
        completed_orders = Order.objects(provider=current_user, status='completed')
        total_earnings = sum([order.total_amount for order in completed_orders])
        
        stats = UserStats(
            total_services=total_services,
            total_orders=total_orders,
            total_reviews=total_reviews,
            average_rating=round(average_rating, 1),
            total_earnings=total_earnings
        )
        
        return ApiResponse(
            data=stats,
            message="User stats retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user stats: {str(e)}"
        )


@router.get("/{user_id}", response_model=ApiResponse[UserResponse], summary="获取指定用户信息")
async def get_user_by_id(user_id: str):
    """获取指定用户的公开信息"""
    try:
        user = User.objects(id=user_id, is_active=True).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return ApiResponse(
            data=UserResponse(**user.to_dict()),
            message="User info retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user info: {str(e)}"
        )
