"""
服务相关API路由
"""
import math
from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Depends, Query
from app.schemas.service import (
    ServiceCreate, ServiceUpdate, ServiceResponse, ServiceFilters, CategoryResponse
)
from app.schemas.common import ApiResponse, PaginationParams, PaginatedResponse, PaginationInfo
from app.models.service import Service
from app.models.category import Category
from app.models.user import User
from app.auth import get_current_active_user, get_current_provider

router = APIRouter(prefix="/services", tags=["服务"])


@router.get("/", response_model=PaginatedResponse[ServiceResponse], summary="获取服务列表")
async def get_services(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    category_id: Optional[str] = Query(None, description="分类ID"),
    location: Optional[str] = Query(None, description="地区"),
    min_price: Optional[float] = Query(None, ge=0, description="最低价格"),
    max_price: Optional[float] = Query(None, ge=0, description="最高价格"),
    min_rating: Optional[float] = Query(None, ge=0, le=5, description="最低评分"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向")
):
    """
    获取服务列表，支持筛选和分页
    
    - **page**: 页码（从1开始）
    - **limit**: 每页数量（1-100）
    - **category_id**: 分类ID筛选
    - **location**: 地区筛选
    - **min_price**: 最低价格
    - **max_price**: 最高价格
    - **min_rating**: 最低评分
    - **search**: 搜索关键词
    - **sort_by**: 排序字段（created_at, price, rating, total_orders）
    - **sort_order**: 排序方向（asc, desc）
    """
    try:
        # 构建查询条件
        query = {"status": "active"}
        
        if category_id:
            query["category"] = category_id
        if location:
            query["location__icontains"] = location
        if min_price is not None:
            query["price__gte"] = min_price
        if max_price is not None:
            query["price__lte"] = max_price
        if min_rating is not None:
            query["rating__gte"] = min_rating
        if search:
            query["$or"] = [
                {"title__icontains": search},
                {"description__icontains": search},
                {"tags__in": [search]}
            ]
        
        # 构建排序
        sort_field = sort_by
        if sort_order == "desc":
            sort_field = f"-{sort_field}"
        
        # 查询总数
        total = Service.objects(**query).count()
        
        # 分页查询
        skip = (page - 1) * limit
        services = Service.objects(**query).order_by(sort_field).skip(skip).limit(limit)
        
        # 转换为响应格式
        service_list = []
        for service in services:
            service_dict = service.to_dict(include_user=True, include_category=True)
            service_list.append(ServiceResponse(**service_dict))
        
        # 计算分页信息
        total_pages = math.ceil(total / limit)
        pagination = PaginationInfo(
            page=page,
            limit=limit,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        return PaginatedResponse(
            data=service_list,
            pagination=pagination,
            message="Services retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get services: {str(e)}"
        )


@router.get("/{service_id}", response_model=ApiResponse[ServiceResponse], summary="获取服务详情")
async def get_service(service_id: str):
    """获取指定服务的详细信息"""
    try:
        service = Service.objects(id=service_id, status="active").first()
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        
        # 增加浏览次数
        service.view_count += 1
        service.save()
        
        service_dict = service.to_dict(include_user=True, include_category=True)
        return ApiResponse(
            data=ServiceResponse(**service_dict),
            message="Service retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get service: {str(e)}"
        )


@router.post("/", response_model=ApiResponse[ServiceResponse], summary="创建服务")
async def create_service(
    service_data: ServiceCreate,
    current_user: User = Depends(get_current_active_user)
):
    """
    创建新服务
    
    - **title**: 服务标题
    - **description**: 服务描述
    - **category_id**: 分类ID
    - **price**: 价格
    - **price_unit**: 价格单位
    - **location**: 服务地点
    - **images**: 图片列表
    - **tags**: 标签列表
    - **features**: 特色列表
    """
    try:
        # 验证分类是否存在
        category = Category.objects(id=service_data.category_id, is_active=True).first()
        if not category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category not found"
            )
        
        # 创建服务
        service = Service(
            title=service_data.title,
            description=service_data.description,
            user=current_user,
            category=category,
            price=service_data.price,
            price_unit=service_data.price_unit,
            location=service_data.location,
            latitude=service_data.latitude,
            longitude=service_data.longitude,
            images=service_data.images or [],
            tags=service_data.tags or [],
            features=service_data.features or []
        )
        service.save()
        
        # 更新分类服务数量
        category.service_count += 1
        category.save()
        
        # 设置用户为服务提供者
        if not current_user.is_provider:
            current_user.is_provider = True
            current_user.save()
        
        service_dict = service.to_dict(include_user=True, include_category=True)
        return ApiResponse(
            data=ServiceResponse(**service_dict),
            message="Service created successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create service: {str(e)}"
        )


@router.put("/{service_id}", response_model=ApiResponse[ServiceResponse], summary="更新服务")
async def update_service(
    service_id: str,
    service_data: ServiceUpdate,
    current_user: User = Depends(get_current_provider)
):
    """更新服务信息"""
    try:
        service = Service.objects(id=service_id, user=current_user).first()
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found or not owned by user"
            )
        
        # 更新字段
        update_fields = service_data.dict(exclude_unset=True)
        for field, value in update_fields.items():
            setattr(service, field, value)
        
        service.save()
        
        service_dict = service.to_dict(include_user=True, include_category=True)
        return ApiResponse(
            data=ServiceResponse(**service_dict),
            message="Service updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update service: {str(e)}"
        )


@router.delete("/{service_id}", response_model=ApiResponse[dict], summary="删除服务")
async def delete_service(
    service_id: str,
    current_user: User = Depends(get_current_provider)
):
    """删除服务"""
    try:
        service = Service.objects(id=service_id, user=current_user).first()
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found or not owned by user"
            )
        
        # 软删除：设置状态为inactive
        service.status = "inactive"
        service.save()
        
        return ApiResponse(
            data={"message": "Service deleted successfully"},
            message="Service deleted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete service: {str(e)}"
        )


@router.get("/categories/", response_model=ApiResponse[List[CategoryResponse]], summary="获取服务分类")
async def get_categories():
    """获取所有活跃的服务分类"""
    try:
        categories = Category.objects(is_active=True).order_by("sort_order", "name")
        category_list = [CategoryResponse(**cat.to_dict()) for cat in categories]
        
        return ApiResponse(
            data=category_list,
            message="Categories retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get categories: {str(e)}"
        )
