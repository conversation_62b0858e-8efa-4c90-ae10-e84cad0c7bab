"""
认证相关API路由
"""
from datetime import timedelta
from fastapi import APIRouter, HTTPException, status, Depends
from app.schemas.auth import UserRegister, UserLogin, Token, PasswordChange, PasswordReset
from app.schemas.common import ApiResponse
from app.models.user import User, UserProfile
from app.auth import authenticate_user, create_access_token, get_current_active_user
from app.config import settings

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/register", response_model=ApiResponse[Token], summary="用户注册")
async def register(user_data: UserRegister):
    """
    用户注册
    
    - **email**: 邮箱地址
    - **password**: 密码（至少6位）
    - **confirm_password**: 确认密码
    - **full_name**: 姓名
    - **phone**: 手机号（可选）
    """
    # 检查邮箱是否已存在
    if User.objects(email=user_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # 检查手机号是否已存在
    if user_data.phone and User.objects(phone=user_data.phone).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Phone number already registered"
        )
    
    try:
        # 创建用户
        user = User(
            email=user_data.email,
            full_name=user_data.full_name,
            phone=user_data.phone
        )
        user.set_password(user_data.password)
        user.save()
        
        # 创建用户资料
        profile = UserProfile(user=user)
        profile.save()
        
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id)}, 
            expires_delta=access_token_expires
        )
        
        return ApiResponse(
            data=Token(
                access_token=access_token,
                token_type="bearer",
                expires_in=settings.access_token_expire_minutes * 60,
                user=user.to_dict()
            ),
            message="Registration successful"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@router.post("/login", response_model=ApiResponse[Token], summary="用户登录")
async def login(user_data: UserLogin):
    """
    用户登录
    
    - **email**: 邮箱地址
    - **password**: 密码
    """
    user = authenticate_user(user_data.email, user_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": str(user.id)}, 
        expires_delta=access_token_expires
    )
    
    return ApiResponse(
        data=Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user=user.to_dict()
        ),
        message="Login successful"
    )


@router.get("/me", response_model=ApiResponse[dict], summary="获取当前用户信息")
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前登录用户的信息"""
    return ApiResponse(
        data=current_user.to_dict(),
        message="User info retrieved successfully"
    )


@router.post("/change-password", response_model=ApiResponse[dict], summary="修改密码")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user)
):
    """
    修改密码
    
    - **current_password**: 当前密码
    - **new_password**: 新密码
    - **confirm_new_password**: 确认新密码
    """
    # 验证当前密码
    if not current_user.verify_password(password_data.current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # 设置新密码
    current_user.set_password(password_data.new_password)
    current_user.save()
    
    return ApiResponse(
        data={"message": "Password changed successfully"},
        message="Password changed successfully"
    )


@router.post("/reset-password", response_model=ApiResponse[dict], summary="重置密码")
async def reset_password(reset_data: PasswordReset):
    """
    重置密码（发送重置邮件）
    
    - **email**: 邮箱地址
    """
    user = User.objects(email=reset_data.email).first()
    if not user:
        # 为了安全，即使用户不存在也返回成功
        return ApiResponse(
            data={"message": "If the email exists, a reset link has been sent"},
            message="Password reset email sent"
        )
    
    # TODO: 实现发送重置邮件的逻辑
    # 这里应该生成重置令牌并发送邮件
    
    return ApiResponse(
        data={"message": "Password reset email sent"},
        message="Password reset email sent"
    )
