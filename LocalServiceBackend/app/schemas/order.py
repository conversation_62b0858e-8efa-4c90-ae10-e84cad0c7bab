"""
订单相关Pydantic模式
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator


class OrderCreate(BaseModel):
    """订单创建模式"""
    service_id: str
    scheduled_date: datetime
    scheduled_time: Optional[str] = None
    notes: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "service_id": "507f1f77bcf86cd799439011",
                "scheduled_date": "2024-01-15T14:00:00",
                "scheduled_time": "14:00-16:00",
                "notes": "请准时到达"
            }
        }


class OrderUpdate(BaseModel):
    """订单更新模式"""
    status: Optional[str] = None
    scheduled_date: Optional[datetime] = None
    scheduled_time: Optional[str] = None
    notes: Optional[str] = None
    provider_notes: Optional[str] = None
    cancellation_reason: Optional[str] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled']
            if v not in allowed_statuses:
                raise ValueError(f'status must be one of {allowed_statuses}')
        return v


class OrderResponse(BaseModel):
    """订单响应模式"""
    id: str
    service_id: str
    customer_id: str
    provider_id: str
    status: str
    scheduled_date: datetime
    scheduled_time: Optional[str] = None
    total_amount: float
    payment_status: str
    payment_method: Optional[str] = None
    notes: Optional[str] = None
    provider_notes: Optional[str] = None
    completed_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    cancellation_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class OrderFilters(BaseModel):
    """订单筛选模式"""
    status: Optional[str] = None
    payment_status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    
    class Config:
        schema_extra = {
            "example": {
                "status": "completed",
                "payment_status": "paid",
                "start_date": "2024-01-01T00:00:00",
                "end_date": "2024-01-31T23:59:59"
            }
        }
