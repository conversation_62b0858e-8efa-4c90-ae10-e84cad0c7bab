"""
认证相关Pydantic模式
"""
from typing import Optional
from pydantic import BaseModel, EmailStr, validator


class UserRegister(BaseModel):
    """用户注册模式"""
    email: EmailStr
    password: str
    confirm_password: str
    full_name: str
    phone: Optional[str] = None
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('passwords do not match')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('password must be at least 6 characters')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "password123",
                "confirm_password": "password123",
                "full_name": "张三",
                "phone": "13800138000"
            }
        }


class UserLogin(BaseModel):
    """用户登录模式"""
    email: EmailStr
    password: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "password123"
            }
        }


class Token(BaseModel):
    """令牌模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 1800,
                "user": {
                    "id": "507f1f77bcf86cd799439011",
                    "email": "<EMAIL>",
                    "full_name": "张三"
                }
            }
        }


class PasswordChange(BaseModel):
    """密码修改模式"""
    current_password: str
    new_password: str
    confirm_new_password: str
    
    @validator('confirm_new_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('passwords do not match')
        return v
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('password must be at least 6 characters')
        return v


class PasswordReset(BaseModel):
    """密码重置模式"""
    email: EmailStr
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }
