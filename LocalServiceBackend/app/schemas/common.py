"""
通用Pydantic模式
"""
from typing import Generic, TypeVar, List, Optional
from pydantic import BaseModel

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """API响应模式"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[T] = None


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = 1
    limit: int = 20
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "limit": 20
            }
        }


class PaginationInfo(BaseModel):
    """分页信息"""
    page: int
    limit: int
    total: int
    total_pages: int
    has_next: bool
    has_prev: bool


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模式"""
    success: bool = True
    message: Optional[str] = None
    data: List[T]
    pagination: PaginationInfo


class ErrorResponse(BaseModel):
    """错误响应模式"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[dict] = None
