"""
服务相关Pydantic模式
"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, validator


class ServiceBase(BaseModel):
    """服务基础模式"""
    title: str
    description: str
    category_id: str
    price: float
    price_unit: str = "hour"
    location: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    
    @validator('price')
    def validate_price(cls, v):
        if v <= 0:
            raise ValueError('price must be greater than 0')
        return v
    
    @validator('price_unit')
    def validate_price_unit(cls, v):
        allowed_units = ['hour', 'day', 'project', 'other']
        if v not in allowed_units:
            raise ValueError(f'price_unit must be one of {allowed_units}')
        return v


class ServiceCreate(ServiceBase):
    """服务创建模式"""
    images: Optional[List[str]] = []
    tags: Optional[List[str]] = []
    features: Optional[List[str]] = []
    
    class Config:
        schema_extra = {
            "example": {
                "title": "专业家电维修服务",
                "description": "提供各种家电维修服务，经验丰富，价格合理",
                "category_id": "507f1f77bcf86cd799439011",
                "price": 120.0,
                "price_unit": "hour",
                "location": "北京市朝阳区",
                "latitude": 39.9042,
                "longitude": 116.4074,
                "images": ["https://example.com/image1.jpg"],
                "tags": ["家电维修", "上门服务"],
                "features": ["24小时服务", "免费检测"]
            }
        }


class ServiceUpdate(BaseModel):
    """服务更新模式"""
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    price_unit: Optional[str] = None
    location: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    images: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    features: Optional[List[str]] = None
    status: Optional[str] = None
    
    @validator('price')
    def validate_price(cls, v):
        if v is not None and v <= 0:
            raise ValueError('price must be greater than 0')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['active', 'inactive', 'suspended']
            if v not in allowed_statuses:
                raise ValueError(f'status must be one of {allowed_statuses}')
        return v


class ServiceResponse(ServiceBase):
    """服务响应模式"""
    id: str
    user_id: str
    images: List[str]
    tags: List[str]
    features: List[str]
    status: str
    rating: float
    total_reviews: int
    total_orders: int
    view_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ServiceFilters(BaseModel):
    """服务筛选模式"""
    category_id: Optional[str] = None
    location: Optional[str] = None
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    min_rating: Optional[float] = None
    search: Optional[str] = None
    sort_by: Optional[str] = "created_at"
    sort_order: Optional[str] = "desc"
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_sorts = ['created_at', 'price', 'rating', 'total_orders']
        if v not in allowed_sorts:
            raise ValueError(f'sort_by must be one of {allowed_sorts}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('sort_order must be asc or desc')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "category_id": "507f1f77bcf86cd799439011",
                "location": "北京",
                "min_price": 50.0,
                "max_price": 200.0,
                "min_rating": 4.0,
                "search": "家电维修",
                "sort_by": "rating",
                "sort_order": "desc"
            }
        }


class CategoryResponse(BaseModel):
    """分类响应模式"""
    id: str
    name: str
    description: Optional[str] = None
    icon: str
    color: str
    slug: str
    service_count: int
    sort_order: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
