"""
用户相关Pydantic模式
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr


class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    avatar_url: Optional[str] = None


class UserCreate(UserBase):
    """用户创建模式"""
    password: str


class UserUpdate(BaseModel):
    """用户更新模式"""
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None


class UserResponse(UserBase):
    """用户响应模式"""
    id: str
    is_verified: bool
    is_active: bool
    is_provider: bool
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "email": "<EMAIL>",
                "full_name": "张三",
                "phone": "13800138000",
                "avatar_url": "https://example.com/avatar.jpg",
                "is_verified": True,
                "is_active": True,
                "is_provider": False,
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "last_login": "2024-01-01T12:00:00"
            }
        }


class UserProfileUpdate(BaseModel):
    """用户资料更新模式"""
    bio: Optional[str] = None
    location: Optional[str] = None


class UserProfileResponse(BaseModel):
    """用户资料响应模式"""
    id: str
    user_id: str
    bio: Optional[str] = None
    location: Optional[str] = None
    rating: float
    total_reviews: int
    total_services: int
    total_orders: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserStats(BaseModel):
    """用户统计模式"""
    total_services: int
    total_orders: int
    total_reviews: int
    average_rating: float
    total_earnings: float
    
    class Config:
        schema_extra = {
            "example": {
                "total_services": 5,
                "total_orders": 20,
                "total_reviews": 18,
                "average_rating": 4.5,
                "total_earnings": 2500.0
            }
        }
