import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  ScrollView,
  FlatList,
  Pressable,
  Image,
  Badge,
  Icon,
  Skeleton,
} from 'native-base';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { serviceService, categoryService } from '../services/api';
import { Service, Category } from '../types';

export const HomeScreen: React.FC = () => {
  const { user } = useAuth();
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, [selectedCategory]);

  const loadData = async () => {
    try {
      setLoading(true);

      // 加载分类
      const categoriesData = await categoryService.getCategories();
      setCategories(categoriesData);

      // 加载服务
      const filters = selectedCategory ? { category_id: selectedCategory } : {};
      const { data: servicesData } = await serviceService.getServices(filters);
      setServices(servicesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <Pressable
      onPress={() => setSelectedCategory(
        selectedCategory === item.id ? null : item.id
      )}
      mr={3}
    >
      <VStack alignItems="center" space={2}>
        <Box
          w={16}
          h={16}
          bg={selectedCategory === item.id ? 'primary.500' : item.color}
          rounded="full"
          justifyContent="center"
          alignItems="center"
        >
          <Icon
            as={MaterialIcons}
            name={item.icon as any}
            size={6}
            color="white"
          />
        </Box>
        <Text
          fontSize="xs"
          textAlign="center"
          color={selectedCategory === item.id ? 'primary.500' : 'gray.600'}
          fontWeight={selectedCategory === item.id ? 'semibold' : 'normal'}
        >
          {item.name}
        </Text>
      </VStack>
    </Pressable>
  );

  const renderServiceItem = ({ item }: { item: Service }) => (
    <Pressable
      onPress={() => {
        // 导航到服务详情页
        console.log('Navigate to service:', item.id);
      }}
      mb={4}
    >
      <Box bg="white" rounded="lg" shadow={2} overflow="hidden">
        {item.images && item.images.length > 0 ? (
          <Image
            source={{ uri: item.images[0] }}
            alt={item.title}
            h={48}
            w="100%"
            resizeMode="cover"
          />
        ) : (
          <Box h={48} bg="gray.200" justifyContent="center" alignItems="center">
            <Icon as={MaterialIcons} name="image" size={8} color="gray.400" />
          </Box>
        )}
        
        <Box p={4}>
          <VStack space={2}>
            <HStack justifyContent="space-between" alignItems="flex-start">
              <Text fontSize="lg" fontWeight="semibold" flex={1} numberOfLines={2}>
                {item.title}
              </Text>
              <Badge colorScheme="primary" rounded="md" ml={2}>
                ¥{item.price}/{item.price_unit === 'hour' ? '小时' : 
                  item.price_unit === 'day' ? '天' : 
                  item.price_unit === 'project' ? '项目' : '次'}
              </Badge>
            </HStack>
            
            <Text fontSize="sm" color="gray.600" numberOfLines={2}>
              {item.description}
            </Text>
            
            <HStack justifyContent="space-between" alignItems="center">
              <HStack alignItems="center" space={1}>
                <Icon as={MaterialIcons} name="location-on" size={4} color="gray.400" />
                <Text fontSize="xs" color="gray.500">
                  {item.location}
                </Text>
              </HStack>
              
              <HStack alignItems="center" space={1}>
                <Icon as={MaterialIcons} name="star" size={4} color="yellow.400" />
                <Text fontSize="xs" color="gray.600">
                  {item.rating.toFixed(1)} ({item.total_reviews})
                </Text>
              </HStack>
            </HStack>
          </VStack>
        </Box>
      </Box>
    </Pressable>
  );

  return (
    <Box flex={1} bg="gray.50">
      {/* Header */}
      <Box bg="white" pt={12} pb={4} px={4} shadow={1}>
        <VStack space={4}>
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                你好，{user?.full_name || '用户'}
              </Text>
              <Text fontSize="sm" color="gray.600">
                发现身边的优质服务
              </Text>
            </VStack>
            <Pressable>
              <Icon as={MaterialIcons} name="notifications" size={6} color="gray.600" />
            </Pressable>
          </HStack>
          
          {/* 搜索框 */}
          <Input
            placeholder="搜索服务..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            InputLeftElement={
              <Icon as={MaterialIcons} name="search" size={5} ml={3} color="gray.400" />
            }
            bg="gray.100"
            borderWidth={0}
            rounded="full"
            size="lg"
          />
        </VStack>
      </Box>

      <ScrollView flex={1} showsVerticalScrollIndicator={false}>
        {/* 分类 */}
        <Box bg="white" py={4}>
          <Text fontSize="lg" fontWeight="semibold" px={4} mb={3}>
            服务分类
          </Text>
          {loading ? (
            <HStack space={3} px={4}>
              {[1, 2, 3, 4].map((i) => (
                <VStack key={i} alignItems="center" space={2}>
                  <Skeleton w={16} h={16} rounded="full" />
                  <Skeleton w={12} h={3} />
                </VStack>
              ))}
            </HStack>
          ) : (
            <FlatList
              data={categories}
              renderItem={renderCategoryItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 16 }}
            />
          )}
        </Box>

        {/* 推荐服务 */}
        <Box flex={1} px={4} py={4}>
          <HStack justifyContent="space-between" alignItems="center" mb={4}>
            <Text fontSize="lg" fontWeight="semibold">
              {selectedCategory ? '筛选结果' : '推荐服务'}
            </Text>
            {selectedCategory && (
              <Pressable onPress={() => setSelectedCategory(null)}>
                <Text fontSize="sm" color="primary.500">
                  清除筛选
                </Text>
              </Pressable>
            )}
          </HStack>
          
          {loading ? (
            <VStack space={4}>
              {[1, 2, 3].map((i) => (
                <Box key={i} bg="white" rounded="lg" overflow="hidden">
                  <Skeleton h={48} w="100%" />
                  <Box p={4}>
                    <Skeleton h={5} w="80%" mb={2} />
                    <Skeleton h={4} w="100%" mb={2} />
                    <Skeleton h={3} w="60%" />
                  </Box>
                </Box>
              ))}
            </VStack>
          ) : (
            <FlatList
              data={services}
              renderItem={renderServiceItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />
          )}
        </Box>
      </ScrollView>
    </Box>
  );
};
