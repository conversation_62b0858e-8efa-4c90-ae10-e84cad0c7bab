/**
 * API测试工具
 * 用于测试前端API集成
 */
import { authService, serviceService, categoryService } from '../services/api';

export const testApiIntegration = async () => {
  console.log('🚀 开始API集成测试...');
  
  try {
    // 测试分类API
    console.log('📂 测试分类API...');
    const categories = await categoryService.getCategories();
    console.log(`✅ 获取到 ${categories.length} 个分类`);
    
    // 测试服务列表API
    console.log('🛍️ 测试服务列表API...');
    const { data: services, pagination } = await serviceService.getServices({
      limit: 5
    });
    console.log(`✅ 获取到 ${services.length} 个服务，总数: ${pagination.total}`);
    
    // 测试服务详情API
    if (services.length > 0) {
      console.log('📋 测试服务详情API...');
      const serviceDetail = await serviceService.getService(services[0].id);
      console.log(`✅ 获取服务详情: ${serviceDetail.title}`);
    }
    
    console.log('🎉 API集成测试完成！');
    return {
      success: true,
      data: {
        categories: categories.length,
        services: services.length,
        totalServices: pagination.total
      }
    };
    
  } catch (error) {
    console.error('❌ API测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export const testAuth = async (email: string, password: string) => {
  console.log('🔐 测试认证功能...');
  
  try {
    // 测试登录
    const { user, token } = await authService.signIn(email, password);
    console.log(`✅ 登录成功: ${user.full_name}`);
    
    // 测试获取当前用户
    const currentUser = await authService.getCurrentUser();
    console.log(`✅ 获取当前用户: ${currentUser.full_name}`);
    
    return {
      success: true,
      user: currentUser
    };
    
  } catch (error) {
    console.error('❌ 认证测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
