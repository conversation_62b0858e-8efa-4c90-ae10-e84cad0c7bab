'use client'

import { useState } from 'react'
import { Ticket, Gift, Clock, ArrowLeft, Search, Filter } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

export default function CouponsPage() {
  const [selectedTab, setSelectedTab] = useState('available')
  const [searchQuery, setSearchQuery] = useState('')

  const coupons = [
    {
      id: 'CPN001',
      title: '家电维修8折券',
      description: '适用于所有家电维修服务，专业师傅上门服务',
      discount: '8折',
      discountType: 'percentage',
      discountValue: 20,
      minAmount: 100,
      maxDiscount: 50,
      expireDate: '2024-02-15',
      status: 'available',
      category: '家电维修',
      source: '新用户礼包',
      usageCount: 0,
      maxUsage: 1
    },
    {
      id: 'CPN002',
      title: '新用户专享券',
      description: '首次使用立减20元，欢迎体验我们的优质服务',
      discount: '减20元',
      discountType: 'fixed',
      discountValue: 20,
      minAmount: 50,
      maxDiscount: 20,
      expireDate: '2024-01-30',
      status: 'available',
      category: '通用',
      source: '注册奖励',
      usageCount: 0,
      maxUsage: 1
    },
    {
      id: 'CPN003',
      title: '清洁服务优惠券',
      description: '专业清洁团队，让您的家焕然一新',
      discount: '9折',
      discountType: 'percentage',
      discountValue: 10,
      minAmount: 80,
      maxDiscount: 30,
      expireDate: '2024-01-20',
      status: 'expired',
      category: '清洁服务',
      source: '活动奖励',
      usageCount: 0,
      maxUsage: 1
    },
    {
      id: 'CPN004',
      title: '美容美发体验券',
      description: '专业造型师上门服务，让您美丽动人',
      discount: '满100减30',
      discountType: 'fixed',
      discountValue: 30,
      minAmount: 100,
      maxDiscount: 30,
      expireDate: '2024-03-01',
      status: 'used',
      category: '美容美发',
      source: '评价奖励',
      usageCount: 1,
      maxUsage: 1,
      usedDate: '2024-01-10'
    },
    {
      id: 'CPN005',
      title: '家教培训优惠券',
      description: '优质教师一对一辅导，提升学习效果',
      discount: '满200减50',
      discountType: 'fixed',
      discountValue: 50,
      minAmount: 200,
      maxDiscount: 50,
      expireDate: '2024-02-28',
      status: 'available',
      category: '家教培训',
      source: '积分兑换',
      usageCount: 0,
      maxUsage: 2
    }
  ]

  const tabs = [
    { id: 'available', label: '可使用', count: coupons.filter(c => c.status === 'available').length },
    { id: 'used', label: '已使用', count: coupons.filter(c => c.status === 'used').length },
    { id: 'expired', label: '已过期', count: coupons.filter(c => c.status === 'expired').length }
  ]

  const getCouponStatus = (status: string) => {
    const statusConfig = {
      available: { label: '可使用', className: 'bg-green-100 text-green-800' },
      used: { label: '已使用', className: 'bg-gray-100 text-gray-800' },
      expired: { label: '已过期', className: 'bg-red-100 text-red-800' }
    }
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Badge className={config.className}>{config.label}</Badge>
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      '家电维修': 'bg-blue-100 text-blue-800',
      '清洁服务': 'bg-green-100 text-green-800',
      '美容美发': 'bg-pink-100 text-pink-800',
      '家教培训': 'bg-purple-100 text-purple-800',
      '通用': 'bg-gray-100 text-gray-800'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const filteredCoupons = coupons.filter(coupon => {
    const matchesTab = coupon.status === selectedTab
    const matchesSearch = coupon.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         coupon.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         coupon.category.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesTab && matchesSearch
  })

  const getDaysUntilExpiry = (expireDate: string) => {
    const today = new Date()
    const expire = new Date(expireDate)
    const diffTime = expire.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">我的优惠券</h1>
          </div>
          <p className="text-gray-600">管理您的所有优惠券</p>
        </div>

        {/* 优惠券统计 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600">{coupons.filter(c => c.status === 'available').length}</div>
              <div className="text-sm text-gray-600">可使用</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-gray-600">{coupons.filter(c => c.status === 'used').length}</div>
              <div className="text-sm text-gray-600">已使用</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-red-600">{coupons.filter(c => c.status === 'expired').length}</div>
              <div className="text-sm text-gray-600">已过期</div>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="search"
                  placeholder="搜索优惠券..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                筛选
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 优惠券状态标签 */}
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={selectedTab === tab.id ? "default" : "outline"}
              onClick={() => setSelectedTab(tab.id)}
              className="flex items-center gap-2"
            >
              {tab.label}
              <Badge variant="secondary" className="ml-1">
                {tab.count}
              </Badge>
            </Button>
          ))}
        </div>

        {/* 优惠券列表 */}
        <div className="space-y-4">
          {filteredCoupons.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-gray-400 mb-4">
                  <Ticket className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无优惠券</h3>
                <p className="text-gray-600 mb-4">您在此分类下还没有优惠券</p>
                <Button asChild>
                  <Link href="/services">去使用服务</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredCoupons.map((coupon) => (
              <Card key={coupon.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <div className="flex">
                    {/* 优惠券左侧 - 折扣信息 */}
                    <div className={`w-32 flex flex-col items-center justify-center text-white relative ${
                      coupon.status === 'available' ? 'bg-gradient-to-br from-red-500 to-pink-600' :
                      coupon.status === 'used' ? 'bg-gray-400' : 'bg-gray-300'
                    }`}>
                      <div className="text-2xl font-bold">{coupon.discount}</div>
                      <div className="text-xs opacity-90">
                        满¥{coupon.minAmount}可用
                      </div>
                      {/* 优惠券齿轮边缘效果 */}
                      <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-gray-50 rounded-full"></div>
                    </div>

                    {/* 优惠券右侧 - 详细信息 */}
                    <div className="flex-1 p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold text-lg text-gray-900 mb-1">
                            {coupon.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-2">
                            {coupon.description}
                          </p>
                        </div>
                        {getCouponStatus(coupon.status)}
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <Badge variant="outline" className={getCategoryColor(coupon.category)}>
                          {coupon.category}
                        </Badge>
                        <span>来源: {coupon.source}</span>
                        {coupon.maxDiscount && (
                          <span>最高优惠: ¥{coupon.maxDiscount}</span>
                        )}
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center text-gray-500">
                            <Clock className="h-4 w-4 mr-1" />
                            {coupon.status === 'expired' ? (
                              <span className="text-red-500">已过期</span>
                            ) : coupon.status === 'used' ? (
                              <span>已于 {coupon.usedDate} 使用</span>
                            ) : (
                              <span>
                                {getDaysUntilExpiry(coupon.expireDate) > 0 
                                  ? `${getDaysUntilExpiry(coupon.expireDate)}天后过期`
                                  : '即将过期'
                                }
                              </span>
                            )}
                          </div>
                          <span className="text-gray-400">|</span>
                          <span className="text-gray-500">
                            已用 {coupon.usageCount}/{coupon.maxUsage} 次
                          </span>
                        </div>

                        <div className="flex space-x-2">
                          {coupon.status === 'available' && (
                            <>
                              <Button size="sm" variant="outline">
                                查看详情
                              </Button>
                              <Button size="sm">
                                立即使用
                              </Button>
                            </>
                          )}
                          {coupon.status === 'used' && (
                            <Button size="sm" variant="outline">
                              查看订单
                            </Button>
                          )}
                          {coupon.status === 'expired' && (
                            <Button size="sm" variant="outline" disabled>
                              已过期
                            </Button>
                          )}
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t text-xs text-gray-500">
                        有效期至: {coupon.expireDate}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* 分页 */}
        {filteredCoupons.length > 0 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              <Button variant="outline" disabled>上一页</Button>
              <Button variant="outline" className="bg-primary text-white">1</Button>
              <Button variant="outline">2</Button>
              <Button variant="outline">下一页</Button>
            </div>
          </div>
        )}

        {/* 获取更多优惠券 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              获取更多优惠券
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl mb-2">🎯</div>
                <h4 className="font-medium mb-2">完成订单</h4>
                <p className="text-sm text-gray-600">完成服务订单后获得优惠券奖励</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl mb-2">⭐</div>
                <h4 className="font-medium mb-2">评价服务</h4>
                <p className="text-sm text-gray-600">对服务进行评价可获得优惠券</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl mb-2">🎁</div>
                <h4 className="font-medium mb-2">积分兑换</h4>
                <p className="text-sm text-gray-600">使用积分兑换各种优惠券</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
