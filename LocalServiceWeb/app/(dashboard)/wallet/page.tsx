'use client'

import { useState } from 'react'
import { Wallet, Plus, ArrowUpRight, ArrowDownLeft, CreditCard, Gift, ArrowLeft, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

export default function WalletPage() {
  const [showBalance, setShowBalance] = useState(true)
  const [selectedTab, setSelectedTab] = useState('all')
  const [rechargeAmount, setRechargeAmount] = useState('')

  const walletData = {
    balance: 1250.80,
    points: 3200,
    coupons: 5,
    frozenAmount: 0
  }

  const transactions = [
    {
      id: 'TXN001',
      type: 'payment',
      title: '支付订单费用',
      description: '家电维修服务 - 张师傅',
      amount: -120.00,
      balance: 1250.80,
      date: '2024-01-15 14:30',
      status: 'completed',
      orderId: 'ORD001'
    },
    {
      id: 'TXN002',
      type: 'recharge',
      title: '钱包充值',
      description: '微信支付充值',
      amount: +200.00,
      balance: 1370.80,
      date: '2024-01-14 10:20',
      status: 'completed',
      orderId: null
    },
    {
      id: 'TXN003',
      type: 'refund',
      title: '订单退款',
      description: '清洁服务取消退款',
      amount: +80.00,
      balance: 1170.80,
      date: '2024-01-13 16:45',
      status: 'completed',
      orderId: 'ORD002'
    },
    {
      id: 'TXN004',
      type: 'payment',
      title: '支付订单费用',
      description: '上门理发服务 - 王师傅',
      amount: -60.00,
      balance: 1090.80,
      date: '2024-01-12 19:15',
      status: 'completed',
      orderId: 'ORD003'
    },
    {
      id: 'TXN005',
      type: 'reward',
      title: '评价奖励',
      description: '完成服务评价获得奖励',
      amount: +10.00,
      balance: 1150.80,
      date: '2024-01-11 20:30',
      status: 'completed',
      orderId: null
    }
  ]

  const coupons = [
    {
      id: 'CPN001',
      title: '家电维修8折券',
      description: '适用于所有家电维修服务',
      discount: '8折',
      minAmount: 100,
      expireDate: '2024-02-15',
      status: 'available'
    },
    {
      id: 'CPN002',
      title: '新用户专享券',
      description: '首次使用立减20元',
      discount: '减20元',
      minAmount: 50,
      expireDate: '2024-01-30',
      status: 'available'
    },
    {
      id: 'CPN003',
      title: '清洁服务优惠券',
      description: '清洁服务专用优惠券',
      discount: '9折',
      minAmount: 80,
      expireDate: '2024-01-20',
      status: 'expired'
    }
  ]

  const tabs = [
    { id: 'all', label: '全部交易', count: transactions.length },
    { id: 'payment', label: '支出', count: transactions.filter(t => t.amount < 0).length },
    { id: 'income', label: '收入', count: transactions.filter(t => t.amount > 0).length }
  ]

  const getTransactionIcon = (type: string) => {
    const icons = {
      payment: <ArrowUpRight className="h-4 w-4 text-red-500" />,
      recharge: <ArrowDownLeft className="h-4 w-4 text-green-500" />,
      refund: <ArrowDownLeft className="h-4 w-4 text-blue-500" />,
      reward: <Gift className="h-4 w-4 text-purple-500" />
    }
    return icons[type as keyof typeof icons] || <Wallet className="h-4 w-4" />
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: '已完成', className: 'bg-green-100 text-green-800' },
      pending: { label: '处理中', className: 'bg-yellow-100 text-yellow-800' },
      failed: { label: '失败', className: 'bg-red-100 text-red-800' }
    }
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Badge className={config.className}>{config.label}</Badge>
  }

  const getCouponStatus = (status: string) => {
    const statusConfig = {
      available: { label: '可使用', className: 'bg-green-100 text-green-800' },
      used: { label: '已使用', className: 'bg-gray-100 text-gray-800' },
      expired: { label: '已过期', className: 'bg-red-100 text-red-800' }
    }
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Badge className={config.className}>{config.label}</Badge>
  }

  const filteredTransactions = transactions.filter(transaction => {
    if (selectedTab === 'all') return true
    if (selectedTab === 'payment') return transaction.amount < 0
    if (selectedTab === 'income') return transaction.amount > 0
    return true
  })

  const quickAmounts = [50, 100, 200, 500]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">我的钱包</h1>
          </div>
          <p className="text-gray-600">管理您的余额、积分和优惠券</p>
        </div>

        {/* 钱包概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Wallet className="h-6 w-6" />
                  <span className="font-medium">余额</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowBalance(!showBalance)}
                  className="text-white hover:bg-white/20"
                >
                  {showBalance ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                </Button>
              </div>
              <div className="text-3xl font-bold mb-2">
                {showBalance ? `¥${walletData.balance.toFixed(2)}` : '****'}
              </div>
              <div className="text-blue-100 text-sm">可用余额</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">积分</p>
                  <p className="text-2xl font-bold text-gray-900">{walletData.points.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">可兑换优惠券</p>
                </div>
                <div className="text-orange-500">
                  <Gift className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">优惠券</p>
                  <p className="text-2xl font-bold text-gray-900">{walletData.coupons}</p>
                  <p className="text-xs text-gray-500">张可用</p>
                </div>
                <div className="text-red-500">
                  <CreditCard className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">冻结金额</p>
                  <p className="text-2xl font-bold text-gray-900">¥{walletData.frozenAmount.toFixed(2)}</p>
                  <p className="text-xs text-gray-500">暂时冻结</p>
                </div>
                <div className="text-gray-500">
                  <Wallet className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 快捷操作 */}
            <Card>
              <CardHeader>
                <CardTitle>快捷操作</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button className="h-20 flex flex-col space-y-2">
                    <Plus className="h-6 w-6" />
                    <span>充值</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col space-y-2">
                    <ArrowUpRight className="h-6 w-6" />
                    <span>提现</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col space-y-2">
                    <Gift className="h-6 w-6" />
                    <span>积分兑换</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col space-y-2">
                    <CreditCard className="h-6 w-6" />
                    <span>优惠券</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 交易记录 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>交易记录</CardTitle>
                <div className="flex space-x-2">
                  {tabs.map((tab) => (
                    <Button
                      key={tab.id}
                      variant={selectedTab === tab.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedTab(tab.id)}
                    >
                      {tab.label} ({tab.count})
                    </Button>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                          {getTransactionIcon(transaction.type)}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{transaction.title}</h4>
                          <p className="text-sm text-gray-600">{transaction.description}</p>
                          <p className="text-xs text-gray-500">{transaction.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {transaction.amount > 0 ? '+' : ''}¥{Math.abs(transaction.amount).toFixed(2)}
                        </div>
                        <div className="text-sm text-gray-500">余额: ¥{transaction.balance.toFixed(2)}</div>
                        {getStatusBadge(transaction.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 充值 */}
            <Card>
              <CardHeader>
                <CardTitle>快速充值</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      充值金额
                    </label>
                    <Input
                      type="number"
                      placeholder="请输入金额"
                      value={rechargeAmount}
                      onChange={(e) => setRechargeAmount(e.target.value)}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {quickAmounts.map((amount) => (
                      <Button
                        key={amount}
                        variant="outline"
                        size="sm"
                        onClick={() => setRechargeAmount(amount.toString())}
                      >
                        ¥{amount}
                      </Button>
                    ))}
                  </div>
                  <Button className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    立即充值
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 我的优惠券 */}
            <Card>
              <CardHeader>
                <CardTitle>我的优惠券</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {coupons.slice(0, 3).map((coupon) => (
                    <div key={coupon.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-sm">{coupon.title}</h4>
                        {getCouponStatus(coupon.status)}
                      </div>
                      <p className="text-xs text-gray-600 mb-2">{coupon.description}</p>
                      <div className="flex justify-between items-center text-xs">
                        <span className="font-bold text-red-600">{coupon.discount}</span>
                        <span className="text-gray-500">满¥{coupon.minAmount}可用</span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        有效期至: {coupon.expireDate}
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" size="sm">
                    查看全部优惠券
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 积分商城 */}
            <Card>
              <CardHeader>
                <CardTitle>积分商城</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-2xl font-bold text-orange-600">{walletData.points}</div>
                  <div className="text-sm text-gray-600">当前积分</div>
                </div>
                <div className="space-y-3">
                  <div className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">10元优惠券</span>
                      <span className="text-sm font-bold text-orange-600">1000积分</span>
                    </div>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">20元优惠券</span>
                      <span className="text-sm font-bold text-orange-600">2000积分</span>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full" size="sm">
                    进入积分商城
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
