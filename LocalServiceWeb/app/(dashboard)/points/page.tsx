'use client'

import { useState } from 'react'
import { Star, Gift, Trophy, ArrowLeft, Calendar, Plus, Minus } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

export default function PointsPage() {
  const [selectedTab, setSelectedTab] = useState('records')

  const pointsData = {
    total: 3200,
    available: 3200,
    used: 1800,
    expired: 200,
    level: 'VIP会员',
    nextLevel: '钻石会员',
    nextLevelPoints: 5000
  }

  const pointsRecords = [
    {
      id: 'PT001',
      type: 'earn',
      title: '完成订单奖励',
      description: '家电维修服务 - 张师傅',
      points: 120,
      date: '2024-01-15 14:30',
      orderId: 'ORD001'
    },
    {
      id: 'PT002',
      type: 'earn',
      title: '评价奖励',
      description: '对服务进行了5星好评',
      points: 50,
      date: '2024-01-15 15:00',
      orderId: 'ORD001'
    },
    {
      id: 'PT003',
      type: 'spend',
      title: '兑换优惠券',
      description: '兑换10元优惠券',
      points: -1000,
      date: '2024-01-14 10:20',
      orderId: null
    },
    {
      id: 'PT004',
      type: 'earn',
      title: '签到奖励',
      description: '连续签到7天奖励',
      points: 100,
      date: '2024-01-13 09:00',
      orderId: null
    },
    {
      id: 'PT005',
      type: 'earn',
      title: '邀请好友',
      description: '成功邀请好友注册',
      points: 200,
      date: '2024-01-12 16:45',
      orderId: null
    }
  ]

  const exchangeItems = [
    {
      id: 'EX001',
      title: '10元优惠券',
      description: '通用优惠券，满50元可用',
      points: 1000,
      originalPrice: 10,
      category: '优惠券',
      stock: 100,
      image: '🎫'
    },
    {
      id: 'EX002',
      title: '20元优惠券',
      description: '通用优惠券，满100元可用',
      points: 2000,
      originalPrice: 20,
      category: '优惠券',
      stock: 50,
      image: '🎫'
    },
    {
      id: 'EX003',
      title: '家电维修8折券',
      description: '专用于家电维修服务',
      points: 1500,
      originalPrice: 15,
      category: '专用券',
      stock: 30,
      image: '🔧'
    },
    {
      id: 'EX004',
      title: '清洁服务9折券',
      description: '专用于清洁服务',
      points: 1200,
      originalPrice: 12,
      category: '专用券',
      stock: 40,
      image: '🧹'
    },
    {
      id: 'EX005',
      title: '实物礼品 - 小米充电宝',
      description: '10000mAh便携充电宝',
      points: 8000,
      originalPrice: 99,
      category: '实物',
      stock: 10,
      image: '🔋'
    },
    {
      id: 'EX006',
      title: '实物礼品 - 蓝牙耳机',
      description: '无线蓝牙耳机，音质清晰',
      points: 12000,
      originalPrice: 149,
      category: '实物',
      stock: 5,
      image: '🎧'
    }
  ]

  const tabs = [
    { id: 'records', label: '积分记录' },
    { id: 'exchange', label: '积分商城' },
    { id: 'rules', label: '积分规则' }
  ]

  const getPointsIcon = (type: string) => {
    return type === 'earn' 
      ? <Plus className="h-4 w-4 text-green-500" />
      : <Minus className="h-4 w-4 text-red-500" />
  }

  const getPointsColor = (type: string) => {
    return type === 'earn' ? 'text-green-600' : 'text-red-600'
  }

  const exchangeItem = (item: any) => {
    if (pointsData.available >= item.points) {
      alert(`成功兑换 ${item.title}！`)
    } else {
      alert('积分不足，无法兑换！')
    }
  }

  const progressPercentage = (pointsData.total / pointsData.nextLevelPoints) * 100

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">我的积分</h1>
          </div>
          <p className="text-gray-600">查看积分余额、记录和兑换商品</p>
        </div>

        {/* 积分概览 */}
        <Card className="mb-8 bg-gradient-to-r from-orange-500 to-red-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Star className="h-6 w-6" />
                  <span className="text-lg font-medium">当前积分</span>
                </div>
                <div className="text-4xl font-bold">{pointsData.total.toLocaleString()}</div>
                <div className="text-orange-100 text-sm">可用积分: {pointsData.available.toLocaleString()}</div>
              </div>
              <div className="text-right">
                <Badge className="bg-white/20 text-white mb-2">{pointsData.level}</Badge>
                <div className="text-sm text-orange-100">
                  距离 {pointsData.nextLevel} 还需 {(pointsData.nextLevelPoints - pointsData.total).toLocaleString()} 积分
                </div>
              </div>
            </div>
            
            {/* 进度条 */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-orange-100 mb-2">
                <span>{pointsData.level}</span>
                <span>{pointsData.nextLevel}</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div 
                  className="bg-white rounded-full h-2 transition-all duration-300"
                  style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{pointsData.used.toLocaleString()}</div>
                <div className="text-orange-100 text-sm">已使用</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{pointsData.expired.toLocaleString()}</div>
                <div className="text-orange-100 text-sm">已过期</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{(pointsData.total + pointsData.used).toLocaleString()}</div>
                <div className="text-orange-100 text-sm">累计获得</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 标签页 */}
        <div className="flex space-x-2 mb-6">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={selectedTab === tab.id ? "default" : "outline"}
              onClick={() => setSelectedTab(tab.id)}
            >
              {tab.label}
            </Button>
          ))}
        </div>

        {/* 积分记录 */}
        {selectedTab === 'records' && (
          <Card>
            <CardHeader>
              <CardTitle>积分记录</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pointsRecords.map((record) => (
                  <div key={record.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                        {getPointsIcon(record.type)}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{record.title}</h4>
                        <p className="text-sm text-gray-600">{record.description}</p>
                        <p className="text-xs text-gray-500">{record.date}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getPointsColor(record.type)}`}>
                        {record.points > 0 ? '+' : ''}{record.points}
                      </div>
                      {record.orderId && (
                        <div className="text-xs text-gray-500">订单: {record.orderId}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 积分商城 */}
        {selectedTab === 'exchange' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {exchangeItems.map((item) => (
                <Card key={item.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="text-center mb-4">
                      <div className="text-4xl mb-2">{item.image}</div>
                      <h3 className="font-semibold text-lg mb-1">{item.title}</h3>
                      <p className="text-gray-600 text-sm">{item.description}</p>
                    </div>
                    
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <div className="text-2xl font-bold text-orange-600">{item.points.toLocaleString()}</div>
                        <div className="text-sm text-gray-500">积分</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500 line-through">¥{item.originalPrice}</div>
                        <Badge variant="outline">{item.category}</Badge>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-600">库存: {item.stock}</span>
                      <span className="text-sm text-gray-600">
                        节省: ¥{item.originalPrice}
                      </span>
                    </div>
                    
                    <Button 
                      className="w-full" 
                      onClick={() => exchangeItem(item)}
                      disabled={pointsData.available < item.points || item.stock === 0}
                    >
                      {pointsData.available < item.points ? '积分不足' : 
                       item.stock === 0 ? '已售罄' : '立即兑换'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* 积分规则 */}
        {selectedTab === 'rules' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  如何获得积分
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">1</div>
                      <div>
                        <h4 className="font-medium">完成订单</h4>
                        <p className="text-sm text-gray-600">每消费1元获得1积分</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">2</div>
                      <div>
                        <h4 className="font-medium">评价服务</h4>
                        <p className="text-sm text-gray-600">每次评价获得50积分</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm">3</div>
                      <div>
                        <h4 className="font-medium">邀请好友</h4>
                        <p className="text-sm text-gray-600">成功邀请获得200积分</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
                      <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm">4</div>
                      <div>
                        <h4 className="font-medium">每日签到</h4>
                        <p className="text-sm text-gray-600">连续签到获得额外奖励</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
                      <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-sm">5</div>
                      <div>
                        <h4 className="font-medium">分享推广</h4>
                        <p className="text-sm text-gray-600">分享服务获得积分奖励</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm">6</div>
                      <div>
                        <h4 className="font-medium">特殊活动</h4>
                        <p className="text-sm text-gray-600">参与活动获得额外积分</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>积分使用规则</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-2">
                    <span className="text-primary">•</span>
                    <span>积分可用于兑换优惠券、实物礼品等</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary">•</span>
                    <span>积分有效期为获得后12个月，过期自动清零</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary">•</span>
                    <span>兑换的商品不支持退换，请谨慎选择</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary">•</span>
                    <span>积分不可转让，仅限本人使用</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary">•</span>
                    <span>平台保留积分规则的最终解释权</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>会员等级权益</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl mb-2">🥉</div>
                    <h4 className="font-medium mb-2">普通会员</h4>
                    <p className="text-sm text-gray-600">0-999积分</p>
                    <p className="text-xs text-gray-500 mt-1">基础积分奖励</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg bg-orange-50">
                    <div className="text-2xl mb-2">🥈</div>
                    <h4 className="font-medium mb-2">VIP会员</h4>
                    <p className="text-sm text-gray-600">1000-4999积分</p>
                    <p className="text-xs text-gray-500 mt-1">1.2倍积分奖励</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl mb-2">🥇</div>
                    <h4 className="font-medium mb-2">钻石会员</h4>
                    <p className="text-sm text-gray-600">5000+积分</p>
                    <p className="text-xs text-gray-500 mt-1">1.5倍积分奖励</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
