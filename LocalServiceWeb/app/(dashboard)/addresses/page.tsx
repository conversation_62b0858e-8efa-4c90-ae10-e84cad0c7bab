'use client'

import { useState } from 'react'
import { MapPin, Plus, Edit, Trash2, ArrowLeft, Home, Building, User } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

export default function AddressesPage() {
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState<any>(null)
  const [addresses, setAddresses] = useState([
    {
      id: '1',
      name: '张三',
      phone: '13800138000',
      address: '北京市朝阳区建国路88号SOHO现代城A座1001室',
      label: 'home',
      isDefault: true,
      detail: '靠近地铁站，大厦正门进入'
    },
    {
      id: '2',
      name: '张三',
      phone: '13800138000',
      address: '北京市海淀区中关村大街1号中关村广场B座2008室',
      label: 'company',
      isDefault: false,
      detail: '工作日9:00-18:00可联系'
    },
    {
      id: '3',
      name: '李女士',
      phone: '13900139000',
      address: '北京市西城区西单北大街120号',
      label: 'other',
      isDefault: false,
      detail: '周末时间，请提前联系'
    }
  ])

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    address: '',
    label: 'home',
    detail: '',
    isDefault: false
  })

  const addressLabels = [
    { value: 'home', label: '家', icon: Home },
    { value: 'company', label: '公司', icon: Building },
    { value: 'other', label: '其他', icon: User }
  ]

  const getLabelInfo = (label: string) => {
    return addressLabels.find(l => l.value === label) || addressLabels[2]
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (editingAddress) {
      // 编辑地址
      setAddresses(addresses.map(addr => 
        addr.id === editingAddress.id 
          ? { ...formData, id: editingAddress.id }
          : formData.isDefault ? { ...addr, isDefault: false } : addr
      ))
      setEditingAddress(null)
    } else {
      // 添加新地址
      const newAddress = {
        ...formData,
        id: Date.now().toString()
      }
      
      setAddresses([
        ...addresses.map(addr => formData.isDefault ? { ...addr, isDefault: false } : addr),
        newAddress
      ])
    }
    
    setFormData({
      name: '',
      phone: '',
      address: '',
      label: 'home',
      detail: '',
      isDefault: false
    })
    setShowAddForm(false)
  }

  const handleEdit = (address: any) => {
    setFormData(address)
    setEditingAddress(address)
    setShowAddForm(true)
  }

  const handleDelete = (addressId: string) => {
    if (confirm('确定要删除这个地址吗？')) {
      setAddresses(addresses.filter(addr => addr.id !== addressId))
    }
  }

  const setAsDefault = (addressId: string) => {
    setAddresses(addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId
    })))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回
                </Button>
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">地址管理</h1>
            </div>
            <Button 
              onClick={() => {
                setShowAddForm(true)
                setEditingAddress(null)
                setFormData({
                  name: '',
                  phone: '',
                  address: '',
                  label: 'home',
                  detail: '',
                  isDefault: false
                })
              }}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              添加地址
            </Button>
          </div>
          <p className="text-gray-600">管理您的收货地址</p>
        </div>

        {/* 添加/编辑地址表单 */}
        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>{editingAddress ? '编辑地址' : '添加新地址'}</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      联系人姓名
                    </label>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="请输入联系人姓名"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      联系电话
                    </label>
                    <Input
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="请输入联系电话"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    详细地址
                  </label>
                  <Input
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="请输入详细地址"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    地址标签
                  </label>
                  <div className="flex space-x-4">
                    {addressLabels.map((label) => {
                      const Icon = label.icon
                      return (
                        <label key={label.value} className="flex items-center cursor-pointer">
                          <input
                            type="radio"
                            name="label"
                            value={label.value}
                            checked={formData.label === label.value}
                            onChange={handleInputChange}
                            className="sr-only"
                          />
                          <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg border-2 transition-colors ${
                            formData.label === label.value 
                              ? 'border-primary bg-primary/10 text-primary' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}>
                            <Icon className="h-4 w-4" />
                            <span>{label.label}</span>
                          </div>
                        </label>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    备注信息
                  </label>
                  <textarea
                    name="detail"
                    value={formData.detail}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="如门牌号、楼层、特殊说明等"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="isDefault"
                    checked={formData.isDefault}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label className="ml-2 text-sm text-gray-700">
                    设为默认地址
                  </label>
                </div>

                <div className="flex space-x-4">
                  <Button type="submit" className="flex-1">
                    {editingAddress ? '保存修改' : '添加地址'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false)
                      setEditingAddress(null)
                    }}
                    className="flex-1"
                  >
                    取消
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* 地址列表 */}
        <div className="space-y-4">
          {addresses.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-gray-400 mb-4">
                  <MapPin className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无地址</h3>
                <p className="text-gray-600 mb-4">您还没有添加任何地址</p>
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加地址
                </Button>
              </CardContent>
            </Card>
          ) : (
            addresses.map((address) => {
              const labelInfo = getLabelInfo(address.label)
              const Icon = labelInfo.icon
              
              return (
                <Card key={address.id} className={`hover:shadow-md transition-shadow ${
                  address.isDefault ? 'ring-2 ring-primary ring-opacity-50' : ''
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="flex items-center space-x-2">
                            <Icon className="h-5 w-5 text-gray-500" />
                            <Badge variant="outline">{labelInfo.label}</Badge>
                          </div>
                          {address.isDefault && (
                            <Badge className="bg-primary text-white">默认</Badge>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center space-x-4">
                            <span className="font-medium text-gray-900">{address.name}</span>
                            <span className="text-gray-600">{address.phone}</span>
                          </div>
                          <div className="flex items-start space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{address.address}</span>
                          </div>
                          {address.detail && (
                            <div className="text-sm text-gray-500 ml-6">
                              {address.detail}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-col space-y-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(address)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          编辑
                        </Button>
                        
                        {!address.isDefault && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setAsDefault(address.id)}
                          >
                            设为默认
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(address.id)}
                          className="text-red-600 hover:text-red-700 hover:border-red-300"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          删除
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>

        {/* 地址管理提示 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>地址管理说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-medium">📍 地址要求</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• 请填写准确的详细地址</li>
                  <li>• 建议添加门牌号、楼层等信息</li>
                  <li>• 确保联系电话畅通</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">🏠 地址标签</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• 家：居住地址</li>
                  <li>• 公司：工作地址</li>
                  <li>• 其他：其他常用地址</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
