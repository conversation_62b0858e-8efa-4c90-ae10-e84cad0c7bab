/**
 * LocalService API 客户端 - Web版本
 * 用于与后端API进行通信
 */

// API配置
const API_CONFIG = {
  BASE_URL: 'http://localhost:8000/api/v1',
  TIMEOUT: 10000,
};

// 存储键名
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  USER_DATA: 'user_data',
} as const;

// API响应类型
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error_code?: string;
  details?: any;
}

interface PaginatedResponse<T = any> {
  success: boolean;
  message?: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// HTTP客户端类
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string, timeout: number = 10000) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  // 获取存储的token
  private getToken(): string | null {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.error('获取token失败:', error);
      return null;
    }
  }

  // 设置token
  private setToken(token: string): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
    } catch (error) {
      console.error('设置token失败:', error);
    }
  }

  // 清除token
  private clearToken(): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_DATA);
    } catch (error) {
      console.error('清除token失败:', error);
    }
  }

  // 通用请求方法
  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getToken();

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 [${endpoint}]:`, error);
      throw error;
    }
  }

  // GET请求
  async get<T = any>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST请求
  async post<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT请求
  async put<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE请求
  async delete<T = any>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // 设置认证token
  setAuthToken(token: string): void {
    this.setToken(token);
  }

  // 清除认证信息
  clearAuth(): void {
    this.clearToken();
  }
}

// 创建API客户端实例
const apiClient = new ApiClient(API_CONFIG.BASE_URL, API_CONFIG.TIMEOUT);

// 认证服务
export const authService = {
  // 用户注册
  async signUp(userData: {
    email: string;
    password: string;
    confirm_password: string;
    full_name: string;
    phone?: string;
  }): Promise<{ user: any; token: string }> {
    const response: ApiResponse = await apiClient.post('/auth/register', userData);
    
    if (response.success && response.data) {
      apiClient.setAuthToken(response.data.access_token);
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));
      }
      return {
        user: response.data.user,
        token: response.data.access_token,
      };
    }
    
    throw new Error(response.message || '注册失败');
  },

  // 用户登录
  async signIn(email: string, password: string): Promise<{ user: any; token: string }> {
    const response: ApiResponse = await apiClient.post('/auth/login', {
      email,
      password,
    });
    
    if (response.success && response.data) {
      apiClient.setAuthToken(response.data.access_token);
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));
      }
      return {
        user: response.data.user,
        token: response.data.access_token,
      };
    }
    
    throw new Error(response.message || '登录失败');
  },

  // 退出登录
  async signOut(): Promise<void> {
    apiClient.clearAuth();
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<any> {
    try {
      const response: ApiResponse = await apiClient.get('/auth/me');
      if (response.success && response.data) {
        if (typeof window !== 'undefined') {
          localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data));
        }
        return response.data;
      }
      return null;
    } catch (error) {
      // 如果获取失败，清除本地认证信息
      apiClient.clearAuth();
      return null;
    }
  },

  // 修改密码
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    const response: ApiResponse = await apiClient.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
      confirm_new_password: newPassword,
    });
    
    if (!response.success) {
      throw new Error(response.message || '密码修改失败');
    }
  },

  // 重置密码
  async resetPassword(email: string): Promise<void> {
    const response: ApiResponse = await apiClient.post('/auth/reset-password', {
      email,
    });
    
    if (!response.success) {
      throw new Error(response.message || '密码重置失败');
    }
  },

  // 检查本地是否有认证信息
  hasLocalAuth(): boolean {
    if (typeof window === 'undefined') return false;
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
      return !!(token && userData);
    } catch {
      return false;
    }
  },

  // 获取本地用户数据
  getLocalUser(): any {
    if (typeof window === 'undefined') return null;
    try {
      const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  },
};

// 用户服务
export const userService = {
  // 获取用户资料
  async getUserProfile(): Promise<any> {
    const response: ApiResponse = await apiClient.get('/users/profile');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取用户资料失败');
  },

  // 更新用户资料
  async updateUserProfile(updates: {
    full_name?: string;
    phone?: string;
    avatar_url?: string;
  }): Promise<any> {
    const response: ApiResponse = await apiClient.put('/users/profile', updates);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新用户资料失败');
  },

  // 获取扩展用户资料
  async getExtendedProfile(): Promise<any> {
    const response: ApiResponse = await apiClient.get('/users/profile/extended');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取扩展资料失败');
  },

  // 更新扩展用户资料
  async updateExtendedProfile(updates: {
    bio?: string;
    location?: string;
  }): Promise<any> {
    const response: ApiResponse = await apiClient.put('/users/profile/extended', updates);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新扩展资料失败');
  },

  // 获取用户统计信息
  async getUserStats(): Promise<any> {
    const response: ApiResponse = await apiClient.get('/users/stats');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取用户统计失败');
  },

  // 获取指定用户信息
  async getUserById(userId: string): Promise<any> {
    const response: ApiResponse = await apiClient.get(`/users/${userId}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取用户信息失败');
  },
};

// 服务相关API
export const serviceService = {
  // 获取服务列表
  async getServices(params: {
    page?: number;
    limit?: number;
    category_id?: string;
    location?: string;
    min_price?: number;
    max_price?: number;
    min_rating?: number;
    search?: string;
    sort_by?: string;
    sort_order?: string;
  } = {}): Promise<{ data: any[]; pagination: any }> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response: PaginatedResponse = await apiClient.get(
      `/services/?${queryParams.toString()}`
    );

    if (response.success) {
      return {
        data: response.data,
        pagination: response.pagination,
      };
    }
    throw new Error(response.message || '获取服务列表失败');
  },

  // 获取服务详情
  async getService(serviceId: string): Promise<any> {
    const response: ApiResponse = await apiClient.get(`/services/${serviceId}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取服务详情失败');
  },

  // 创建服务
  async createService(serviceData: {
    title: string;
    description: string;
    category_id: string;
    price: number;
    price_unit: string;
    location: string;
    latitude?: number;
    longitude?: number;
    images?: string[];
    tags?: string[];
    features?: string[];
  }): Promise<any> {
    const response: ApiResponse = await apiClient.post('/services/', serviceData);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '创建服务失败');
  },

  // 更新服务
  async updateService(serviceId: string, updates: any): Promise<any> {
    const response: ApiResponse = await apiClient.put(`/services/${serviceId}`, updates);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新服务失败');
  },

  // 删除服务
  async deleteService(serviceId: string): Promise<void> {
    const response: ApiResponse = await apiClient.delete(`/services/${serviceId}`);
    if (!response.success) {
      throw new Error(response.message || '删除服务失败');
    }
  },
};

// 分类服务
export const categoryService = {
  // 获取所有分类
  async getCategories(): Promise<any[]> {
    const response: ApiResponse = await apiClient.get('/services/categories/');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取分类失败');
  },
};

// 兼容性函数 - 保持与原有代码的兼容性
export async function getCurrentUser() {
  return authService.getCurrentUser();
}

export async function signOut() {
  return authService.signOut();
}

export async function signInWithEmail(email: string, password: string) {
  const result = await authService.signIn(email, password);
  return {
    user: result.user,
    session: { access_token: result.token }
  };
}

export async function signUpWithEmail(email: string, password: string, metadata?: any) {
  const result = await authService.signUp({
    email,
    password,
    confirm_password: password,
    full_name: metadata?.full_name || '',
    phone: metadata?.phone,
  });
  return {
    user: result.user,
    session: { access_token: result.token }
  };
}

export async function resetPassword(email: string) {
  return authService.resetPassword(email);
}

export async function getServices(filters?: {
  category?: string;
  location?: string;
  priceMin?: number;
  priceMax?: number;
  search?: string;
  limit?: number;
  offset?: number;
}) {
  const params: any = {};

  if (filters?.category) params.category_id = filters.category;
  if (filters?.location) params.location = filters.location;
  if (filters?.priceMin) params.min_price = filters.priceMin;
  if (filters?.priceMax) params.max_price = filters.priceMax;
  if (filters?.search) params.search = filters.search;
  if (filters?.limit) params.limit = filters.limit;
  if (filters?.offset) params.page = Math.floor(filters.offset / (filters.limit || 20)) + 1;

  const result = await serviceService.getServices(params);
  return result.data;
}

export async function getServiceById(id: string) {
  return serviceService.getService(id);
}

export async function createService(service: any) {
  return serviceService.createService(service);
}

export async function updateService(id: string, updates: any) {
  return serviceService.updateService(id, updates);
}

export async function deleteService(id: string) {
  return serviceService.deleteService(id);
}

export { apiClient, ApiResponse, PaginatedResponse };
