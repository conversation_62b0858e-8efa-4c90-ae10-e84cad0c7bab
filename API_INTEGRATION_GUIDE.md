# LocalService API 集成指南

## 📋 概述

本指南说明如何将 LocalServiceApp (移动端) 和 LocalServiceWeb (网页端) 与新的 FastAPI 后端进行集成。

## 🔧 后端API配置

### API服务地址
- **开发环境**: `http://localhost:8000/api/v1`
- **生产环境**: 根据部署配置修改

### 主要API端点
- **认证**: `/auth/`
- **用户**: `/users/`
- **服务**: `/services/`

## 📱 移动端集成 (LocalServiceApp)

### 1. API服务配置

新的API服务位于 `src/services/api.ts`，包含：

- `authService` - 认证相关
- `userService` - 用户资料管理
- `serviceService` - 服务管理
- `categoryService` - 分类管理

### 2. 认证上下文更新

`src/contexts/AuthContext.tsx` 已更新为使用新的API：

```typescript
// 登录
const { user, token } = await authService.signIn(email, password);

// 注册
const { user, token } = await authService.signUp({
  email,
  password,
  confirm_password: password,
  full_name: 'User Name',
  phone: '1234567890'
});

// 获取当前用户
const user = await authService.getCurrentUser();
```

### 3. 数据获取示例

```typescript
// 获取服务列表
const { data, pagination } = await serviceService.getServices({
  page: 1,
  limit: 20,
  category_id: 'category-id',
  search: '搜索关键词'
});

// 获取分类
const categories = await categoryService.getCategories();

// 获取服务详情
const service = await serviceService.getService(serviceId);
```

### 4. 测试API集成

使用测试工具验证集成：

```typescript
import { testApiIntegration, testAuth } from '../utils/testApi';

// 测试基础API
const result = await testApiIntegration();

// 测试认证
const authResult = await testAuth('<EMAIL>', 'admin123');
```

## 🌐 网页端集成 (LocalServiceWeb)

### 1. API服务配置

新的API服务位于 `lib/api.ts`，提供与移动端相同的功能。

### 2. 组件更新示例

`components/home/<USER>

```typescript
'use client'

import { useState, useEffect } from 'react'
import { serviceService } from '../../lib/api'

export function FeaturedServices() {
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadServices()
  }, [])

  const loadServices = async () => {
    try {
      const { data } = await serviceService.getServices({
        limit: 4,
        sort_by: 'rating',
        sort_order: 'desc'
      })
      setServices(data)
    } catch (error) {
      console.error('Error loading services:', error)
    } finally {
      setLoading(false)
    }
  }
  
  // 渲染逻辑...
}
```

### 3. 兼容性函数

为保持与现有代码的兼容性，提供了兼容性函数：

```typescript
// 原有函数调用方式仍然有效
const services = await getServices({ limit: 10 });
const user = await getCurrentUser();
await signInWithEmail(email, password);
```

## 🔑 认证流程

### 1. 用户注册
```typescript
const userData = {
  email: '<EMAIL>',
  password: 'password123',
  confirm_password: 'password123',
  full_name: '用户姓名',
  phone: '1234567890'
};

const { user, token } = await authService.signUp(userData);
```

### 2. 用户登录
```typescript
const { user, token } = await authService.signIn(email, password);
```

### 3. 获取用户信息
```typescript
// 基础用户信息
const user = await authService.getCurrentUser();

// 用户资料
const profile = await userService.getUserProfile();

// 扩展资料
const extendedProfile = await userService.getExtendedProfile();
```

## 📊 数据格式

### 用户数据
```typescript
interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  avatar_url?: string;
  is_verified: boolean;
  is_provider: boolean;
  created_at: string;
  updated_at: string;
}
```

### 服务数据
```typescript
interface Service {
  id: string;
  title: string;
  description: string;
  price: number;
  price_unit: string;
  location: string;
  rating: number;
  total_reviews: number;
  total_orders: number;
  images: string[];
  tags: string[];
  features: string[];
  user: User;
  category: Category;
}
```

### 分类数据
```typescript
interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  service_count: number;
}
```

## 🚀 启动指南

### 1. 启动后端API
```bash
cd LocalServiceBackend
python main.py
```

### 2. 启动移动端
```bash
cd LocalServiceApp
npm start
```

### 3. 启动网页端
```bash
cd LocalServiceWeb
npm run dev
```

## 🧪 测试账户

- **管理员**: <EMAIL> / admin123
- **普通用户**: 任意用户 / 123456

## 📝 注意事项

1. **API地址配置**: 确保前端配置的API地址与后端服务地址一致
2. **CORS设置**: 后端已配置CORS，支持前端跨域请求
3. **错误处理**: 所有API调用都包含错误处理，失败时会抛出异常
4. **认证状态**: 认证状态会自动保存到本地存储
5. **数据缓存**: 考虑添加适当的数据缓存机制提升性能

## 🔧 故障排除

### 常见问题

1. **连接失败**: 检查后端API是否正常运行
2. **认证失败**: 确认用户名密码正确，检查token是否有效
3. **数据加载失败**: 检查网络连接和API响应

### 调试工具

- 使用浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 使用API测试工具验证后端接口

## 📚 相关文档

- [后端API文档](http://localhost:8000/docs)
- [项目总结](LocalServiceBackend/PROJECT_SUMMARY.md)
- [后端README](LocalServiceBackend/README.md)
